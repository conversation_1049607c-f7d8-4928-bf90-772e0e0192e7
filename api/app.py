#!/usr/bin/env python3
"""
Enhanced Lead Processing API

Flask application providing enhanced lead processing capabilities including:
- Advanced lead scoring and qualification
- Intelligent follow-up automation
- Workflow orchestration
- Lead management endpoints
"""

import os
import sys
import logging
from datetime import datetime
from flask import Flask, jsonify, request
from flask_cors import CORS

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Import route blueprints
from api.routes.enhanced_leads import enhanced_leads_bp
from api.routes.workflows import workflows_bp

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_app():
    """Create and configure the Flask application"""
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['DEBUG'] = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    
    # Enable CORS
    CORS(app, origins=['*'])
    
    # Register blueprints
    app.register_blueprint(enhanced_leads_bp)
    app.register_blueprint(workflows_bp)
    
    # Health check endpoint
    @app.route('/api/v1/health', methods=['GET'])
    def health_check():
        """API health check endpoint"""
        return jsonify({
            "status": "healthy",
            "service": "enhanced_lead_processing_api",
            "version": "1.0.0",
            "timestamp": datetime.utcnow().isoformat(),
            "endpoints": {
                "enhanced_leads": "/api/v1/leads/*",
                "workflows": "/api/v1/workflows/*"
            }
        }), 200
    
    # Root endpoint
    @app.route('/', methods=['GET'])
    def root():
        """Root endpoint with API information"""
        return jsonify({
            "message": "Enhanced Lead Processing API",
            "version": "1.0.0",
            "documentation": "/api/v1/health",
            "endpoints": {
                "health": "/api/v1/health",
                "enhanced_leads": "/api/v1/leads/",
                "workflows": "/api/v1/workflows/"
            }
        }), 200
    
    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            "error": "Not Found",
            "message": "The requested endpoint was not found",
            "status_code": 404
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        logger.error(f"Internal server error: {str(error)}")
        return jsonify({
            "error": "Internal Server Error",
            "message": "An internal server error occurred",
            "status_code": 500
        }), 500
    
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({
            "error": "Bad Request",
            "message": "The request was invalid",
            "status_code": 400
        }), 400
    
    # Request logging middleware
    @app.before_request
    def log_request_info():
        logger.info(f"Request: {request.method} {request.url}")
        if request.is_json:
            logger.debug(f"Request JSON: {request.get_json()}")
    
    @app.after_request
    def log_response_info(response):
        logger.info(f"Response: {response.status_code}")
        return response
    
    return app

def main():
    """Main function to run the application"""
    app = create_app()
    
    # Get configuration from environment
    host = os.getenv('API_HOST', '0.0.0.0')
    port = int(os.getenv('API_PORT', 5002))
    debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    
    logger.info(f"Starting Enhanced Lead Processing API on {host}:{port}")
    logger.info(f"Debug mode: {debug}")
    
    try:
        app.run(host=host, port=port, debug=debug)
    except Exception as e:
        logger.error(f"Failed to start API server: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
