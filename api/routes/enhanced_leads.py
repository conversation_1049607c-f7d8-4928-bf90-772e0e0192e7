#!/usr/bin/env python3
"""
Enhanced Lead Processing API Routes

This module provides API endpoints for the enhanced lead processing system including:
- Advanced lead scoring and qualification
- Intelligent follow-up automation
- Lead processing workflow triggers
- Batch processing capabilities
"""

import os
import logging
import json
from datetime import datetime
from typing import Dict, Any, List, Optional

from flask import Blueprint, request, jsonify
from flask_cors import cross_origin

# Import the enhanced lead processing modules
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from agents.workflows.enhanced_lead_processor import (
    EnhancedLeadProcessor, 
    process_lead_enhanced,
    batch_process_leads
)
from agents.workflows.intelligent_followup import (
    IntelligentFollowUp,
    create_intelligent_followup,
    track_lead_response
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create Blueprint
enhanced_leads_bp = Blueprint('enhanced_leads', __name__, url_prefix='/api/v1/leads')

@enhanced_leads_bp.route('/process-enhanced', methods=['POST'])
@cross_origin()
def process_lead_enhanced_endpoint():
    """
    Process a lead with enhanced automation
    
    Expected JSON payload:
    {
        "lead_id": "string",
        "lead_data": "string (JSON)" or object
    }
    
    Returns:
        JSON response with processing results
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400
        
        lead_id = data.get('lead_id')
        if not lead_id:
            return jsonify({
                "success": False,
                "error": "lead_id is required"
            }), 400
        
        # Parse lead_data if it's a string
        lead_data = data.get('lead_data', {})
        if isinstance(lead_data, str):
            try:
                lead_data = json.loads(lead_data)
            except json.JSONDecodeError:
                return jsonify({
                    "success": False,
                    "error": "Invalid JSON in lead_data"
                }), 400
        
        logger.info(f"Processing enhanced lead: {lead_id}")
        
        # Process the lead
        result = process_lead_enhanced(lead_id)
        
        if result.get('success'):
            logger.info(f"Successfully processed lead {lead_id}")
            return jsonify(result), 200
        else:
            logger.error(f"Failed to process lead {lead_id}: {result.get('error')}")
            return jsonify(result), 500
            
    except Exception as e:
        logger.error(f"Error in process_lead_enhanced_endpoint: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500

@enhanced_leads_bp.route('/batch-process', methods=['POST'])
@cross_origin()
def batch_process_leads_endpoint():
    """
    Process multiple leads with enhanced automation
    
    Expected JSON payload:
    {
        "lead_ids": ["string", "string", ...]
    }
    
    Returns:
        JSON response with batch processing results
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400
        
        lead_ids = data.get('lead_ids', [])
        if not lead_ids or not isinstance(lead_ids, list):
            return jsonify({
                "success": False,
                "error": "lead_ids must be a non-empty list"
            }), 400
        
        logger.info(f"Batch processing {len(lead_ids)} leads")
        
        # Process the leads
        results = batch_process_leads(lead_ids)
        
        # Calculate summary statistics
        successful = sum(1 for result in results.values() if result.get('success'))
        total = len(results)
        
        return jsonify({
            "success": True,
            "total_leads": total,
            "successful_leads": successful,
            "failed_leads": total - successful,
            "results": results,
            "timestamp": datetime.utcnow().isoformat()
        }), 200
            
    except Exception as e:
        logger.error(f"Error in batch_process_leads_endpoint: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500

@enhanced_leads_bp.route('/score', methods=['POST'])
@cross_origin()
def score_lead_endpoint():
    """
    Calculate advanced lead score
    
    Expected JSON payload:
    {
        "lead_id": "string"
    }
    
    Returns:
        JSON response with lead scoring results
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400
        
        lead_id = data.get('lead_id')
        if not lead_id:
            return jsonify({
                "success": False,
                "error": "lead_id is required"
            }), 400
        
        logger.info(f"Calculating score for lead: {lead_id}")
        
        # Create processor and calculate score
        processor = EnhancedLeadProcessor(lead_id=lead_id)
        lead_score = processor.calculate_advanced_score()
        
        return jsonify({
            "success": True,
            "lead_id": lead_id,
            "score": {
                "total_score": lead_score.total_score,
                "tier": lead_score.tier,
                "priority": lead_score.priority.value,
                "confidence": lead_score.confidence,
                "factors": lead_score.factors,
                "recommendations": lead_score.recommendations,
                "next_actions": lead_score.next_actions
            },
            "timestamp": datetime.utcnow().isoformat()
        }), 200
            
    except Exception as e:
        logger.error(f"Error in score_lead_endpoint: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500

@enhanced_leads_bp.route('/qualify', methods=['POST'])
@cross_origin()
def qualify_lead_endpoint():
    """
    Perform lead qualification
    
    Expected JSON payload:
    {
        "lead_id": "string"
    }
    
    Returns:
        JSON response with qualification results
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400
        
        lead_id = data.get('lead_id')
        if not lead_id:
            return jsonify({
                "success": False,
                "error": "lead_id is required"
            }), 400
        
        logger.info(f"Qualifying lead: {lead_id}")
        
        # Create processor and qualify lead
        processor = EnhancedLeadProcessor(lead_id=lead_id)
        qualification = processor.qualify_lead()
        
        return jsonify({
            "success": True,
            "lead_id": lead_id,
            "qualification": {
                "qualified": qualification.qualified,
                "score": qualification.qualification_score,
                "reasons": qualification.disqualification_reasons,
                "factors": qualification.qualification_factors,
                "recommended_actions": qualification.recommended_actions
            },
            "timestamp": datetime.utcnow().isoformat()
        }), 200
            
    except Exception as e:
        logger.error(f"Error in qualify_lead_endpoint: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500

@enhanced_leads_bp.route('/followup/create', methods=['POST'])
@cross_origin()
def create_followup_endpoint():
    """
    Create intelligent follow-up sequence
    
    Expected JSON payload:
    {
        "lead_id": "string",
        "priority": "string" (optional),
        "tier": integer (optional)
    }
    
    Returns:
        JSON response with follow-up sequence creation results
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400
        
        lead_id = data.get('lead_id')
        if not lead_id:
            return jsonify({
                "success": False,
                "error": "lead_id is required"
            }), 400
        
        priority = data.get('priority', 'medium')
        tier = data.get('tier', 2)
        
        logger.info(f"Creating follow-up sequence for lead: {lead_id}")
        
        # Create follow-up sequence
        result = create_intelligent_followup(lead_id, priority, tier)
        
        return jsonify(result), 200 if result.get('success') else 500
            
    except Exception as e:
        logger.error(f"Error in create_followup_endpoint: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500

@enhanced_leads_bp.route('/followup/track-response', methods=['POST'])
@cross_origin()
def track_response_endpoint():
    """
    Track lead response and adapt strategy
    
    Expected JSON payload:
    {
        "lead_id": "string",
        "response_type": "string",
        "response_content": "string" (optional)
    }
    
    Returns:
        JSON response with tracking results
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400
        
        lead_id = data.get('lead_id')
        response_type = data.get('response_type')
        
        if not lead_id or not response_type:
            return jsonify({
                "success": False,
                "error": "lead_id and response_type are required"
            }), 400
        
        response_content = data.get('response_content', '')
        
        logger.info(f"Tracking response for lead: {lead_id}, type: {response_type}")
        
        # Track the response
        result = track_lead_response(lead_id, response_type, response_content)
        
        return jsonify(result), 200 if result.get('response_tracked') else 500
            
    except Exception as e:
        logger.error(f"Error in track_response_endpoint: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500

@enhanced_leads_bp.route('/health', methods=['GET'])
@cross_origin()
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "enhanced_leads_api",
        "timestamp": datetime.utcnow().isoformat()
    }), 200
