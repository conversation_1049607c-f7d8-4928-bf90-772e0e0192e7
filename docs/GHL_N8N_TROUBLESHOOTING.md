# GHL to n8n Integration Troubleshooting Guide

## Quick Setup

To get the GHL to n8n workflow working quickly:

```bash
# Run the complete setup
./scripts/setup_ghl_integration.sh

# Or run individual steps
./scripts/setup_ghl_integration.sh prerequisites
./scripts/setup_ghl_integration.sh services
./scripts/setup_ghl_integration.sh workflows
./scripts/setup_ghl_integration.sh test
```

## Common Issues and Solutions

### 1. n8n Not Accessible

**Symptoms:**
- Cannot access n8n at http://localhost:5678
- Webhook endpoints return connection errors

**Solutions:**
```bash
# Check if n8n container is running
docker-compose ps

# Check n8n logs
docker-compose logs n8n

# Restart n8n service
docker-compose restart n8n

# Check if port 5678 is available
netstat -tulpn | grep 5678
```

### 2. Workflow Import Failures

**Symptoms:**
- Workflows not appearing in n8n dashboard
- API key errors during deployment

**Solutions:**
```bash
# Set n8n API key
export N8N_API_KEY="your_api_key"

# Manually import workflows
./scripts/deploy_n8n_workflows.sh

# Check workflow files are valid JSON
jq . ghl-lead-workflow.json
jq . n8n/workflows/*.json
```

### 3. Database Connection Issues

**Symptoms:**
- Postgres connection errors in n8n logs
- Workflow executions fail with database errors

**Solutions:**
```bash
# Check postgres container
docker-compose logs postgres

# Test database connection
docker-compose exec postgres psql -U n8n -d n8n -c "SELECT 1;"

# Reset database
docker-compose down -v
docker-compose up -d
```

### 4. Webhook Not Receiving Data

**Symptoms:**
- GHL sends webhooks but n8n doesn't receive them
- Webhook URL returns 404 or 500 errors

**Solutions:**
```bash
# Test webhook endpoint manually
curl -X POST http://localhost:5678/webhook/ghl/lead \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'

# Check n8n execution logs
# Go to n8n dashboard > Executions

# Verify webhook is active
# Go to n8n dashboard > Workflows > GHL Lead → Supabase Production
```

### 5. Supabase Connection Issues

**Symptoms:**
- Leads not appearing in Supabase
- Database insert errors in n8n logs

**Solutions:**
```bash
# Verify Supabase credentials in .env
cat .env | grep SUPABASE

# Test Supabase connection
curl -H "apikey: $SUPABASE_KEY" \
     -H "Authorization: Bearer $SUPABASE_KEY" \
     "$SUPABASE_URL/rest/v1/leads?select=*&limit=1"

# Check Supabase credentials in n8n
# Go to n8n dashboard > Credentials > Supabase Connection
```

## Environment Variables Checklist

Ensure these variables are set in your `.env` file:

```bash
# Required for n8n
N8N_API_KEY=your_n8n_api_key
WEBHOOK_URL=http://localhost:5678

# Required for Supabase integration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_key

# Required for GHL integration
GHL_API_KEY=your_ghl_api_key
GHL_LOCATION_ID=your_ghl_location_id
GHL_LOCATION_API_KEY=your_ghl_location_api_key
```

## Testing the Integration

### Manual Test
```bash
# Run the test script
python3 scripts/test_ghl_workflow.py

# Or test manually
curl -X POST http://localhost:5678/webhook/ghl/lead \
  -H "Content-Type: application/json" \
  -d '{
    "contact": {
      "id": "test_123",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phone": "+1234567890"
    }
  }'
```

### Check Results
1. Go to n8n dashboard: http://localhost:5678
2. Navigate to Executions
3. Look for successful execution of "GHL Lead → Supabase Production"
4. Check Supabase dashboard for new lead entry

## Production Deployment

For production deployment:

```bash
# Set your domain
export DOMAIN=your-domain.com

# Run production deployment
./deploy-production.sh
```

This will:
- Configure SSL with Caddy
- Update webhook URLs to use your domain
- Deploy all workflows
- Test the endpoints

## Monitoring and Logs

### n8n Logs
```bash
# View n8n container logs
docker-compose logs -f n8n

# View specific workflow execution logs
# Go to n8n dashboard > Executions > Click on execution
```

### API Logs
```bash
# View API container logs
docker-compose logs -f api

# View all logs
docker-compose logs -f
```

### Webhook Testing
```bash
# Test webhook endpoint
curl -X POST https://your-domain.com/webhook/ghl/lead \
  -H "Content-Type: application/json" \
  -d '{"test": true}'
```

## Getting Help

If you're still having issues:

1. Check the logs: `docker-compose logs -f`
2. Verify your .env configuration
3. Test each component individually
4. Check the n8n dashboard for workflow status
5. Verify GHL webhook configuration

## Useful Commands

```bash
# Complete reset
docker-compose down -v
docker-compose up -d
./scripts/setup_ghl_integration.sh

# View service status
docker-compose ps

# Restart specific service
docker-compose restart n8n

# View real-time logs
docker-compose logs -f n8n

# Test workflow
python3 scripts/test_ghl_workflow.py

# Deploy workflows only
./scripts/deploy_n8n_workflows.sh
```
