# Supabase Manual Migration Guide

This guide provides step-by-step instructions for manually creating the database schema using the Supabase UI when direct SQL execution is not available.

## Prerequisites

1. Access to your Supabase project dashboard
2. Admin privileges to create tables and modify the database schema

## Overview

The AI-OS system requires several tables to function properly:

1. `kb_documents` - For storing knowledge base documents with vector embeddings
2. `leads` - For storing contact information from GoHighLevel
3. `activities` - For tracking interactions and status changes
4. `offers` - For storing generated offers and their status
5. `system_config` - For configuration overrides
6. `agent_errors` - For error logging
7. `webhook_logs` - For debugging and auditing

This guide will walk you through creating each table and its associated indexes using the Supabase UI.

## Step 1: Enable Required Extensions

First, we need to enable the required PostgreSQL extensions:

1. Navigate to the **Database** section in your Supabase dashboard
2. Click on **Extensions** in the sidebar
3. Find and enable the following extensions:
   - `uuid-ossp` - For UUID generation
   - `vector` - For vector embeddings support

## Step 2: Create the Knowledge Base Documents Table

1. Navigate to the **Table Editor** in your Supabase dashboard
2. Click **Create a new table**
3. Enter the following details:
   - **Name**: `kb_documents`
   - **Enable Row Level Security (RLS)**: Checked
   - **Columns**:

| Name | Type | Default Value | Primary | Nullable |
|------|------|--------------|---------|----------|
| id | uuid | uuid_generate_v4() | ✓ | ✗ |
| title | text | | ✗ | ✗ |
| content | text | | ✗ | ✗ |
| metadata | jsonb | '{}' | ✗ | ✓ |
| created_at | timestamptz | now() | ✗ | ✓ |
| updated_at | timestamptz | now() | ✗ | ✓ |
| status | text | 'active' | ✗ | ✓ |
| source | text | | ✗ | ✓ |
| category | text | | ✗ | ✓ |
| tags | text[] | | ✗ | ✓ |

4. Click **Save** to create the table

5. After creating the table, we need to add the vector embedding column:
   - Go to the **SQL Editor** in your Supabase dashboard
   - Run the following SQL:

```sql
ALTER TABLE kb_documents ADD COLUMN embedding VECTOR(1536);
```

6. Create indexes for the `kb_documents` table:
   - Go to the **SQL Editor** in your Supabase dashboard
   - Run the following SQL statements one by one:

```sql
CREATE INDEX idx_kb_documents_status ON kb_documents(status);
CREATE INDEX idx_kb_documents_category ON kb_documents(category);
CREATE INDEX idx_kb_documents_tags ON kb_documents USING GIN(tags);
CREATE INDEX idx_kb_documents_embedding ON kb_documents USING ivfflat (embedding vector_cosine_ops);
```

## Step 3: Create the Leads Table

1. Navigate to the **Table Editor** in your Supabase dashboard
2. Click **Create a new table**
3. Enter the following details:
   - **Name**: `leads`
   - **Enable Row Level Security (RLS)**: Checked
   - **Columns**:

| Name | Type | Default Value | Primary | Nullable |
|------|------|--------------|---------|----------|
| id | uuid | uuid_generate_v4() | ✓ | ✗ |
| ghl_id | text | | ✗ | ✗ |
| first_name | text | | ✗ | ✓ |
| last_name | text | | ✗ | ✓ |
| email | text | | ✗ | ✓ |
| phone | text | | ✗ | ✓ |
| address | jsonb | '{}' | ✗ | ✓ |
| tags | text[] | | ✗ | ✓ |
| status | text | 'new' | ✗ | ✓ |
| tier | integer | 3 | ✗ | ✓ |
| source | text | | ✗ | ✓ |
| created_at | timestamptz | now() | ✗ | ✓ |
| updated_at | timestamptz | now() | ✗ | ✓ |
| last_contacted_at | timestamptz | | ✗ | ✓ |
| metadata | jsonb | '{}' | ✗ | ✓ |

4. Click **Save** to create the table

5. Add a unique constraint to the `ghl_id` column:
   - Go to the **SQL Editor** in your Supabase dashboard
   - Run the following SQL:

```sql
ALTER TABLE leads ADD CONSTRAINT leads_ghl_id_key UNIQUE (ghl_id);
```

6. Create indexes for the `leads` table:
   - Go to the **SQL Editor** in your Supabase dashboard
   - Run the following SQL statements one by one:

```sql
CREATE INDEX idx_leads_status ON leads(status);
CREATE INDEX idx_leads_tier ON leads(tier);
CREATE INDEX idx_leads_tags ON leads USING GIN(tags);
CREATE INDEX idx_leads_created_at ON leads(created_at);
CREATE INDEX idx_leads_last_contacted_at ON leads(last_contacted_at);
```

## Step 4: Create the Activities Table

1. Navigate to the **Table Editor** in your Supabase dashboard
2. Click **Create a new table**
3. Enter the following details:
   - **Name**: `activities`
   - **Enable Row Level Security (RLS)**: Checked
   - **Columns**:

| Name | Type | Default Value | Primary | Nullable |
|------|------|--------------|---------|----------|
| id | uuid | uuid_generate_v4() | ✓ | ✗ |
| lead_id | uuid | | ✗ | ✗ |
| activity_type | text | | ✗ | ✗ |
| description | text | | ✗ | ✓ |
| created_at | timestamptz | now() | ✗ | ✓ |
| metadata | jsonb | '{}' | ✗ | ✓ |
| source | text | | ✗ | ✓ |
| agent | text | | ✗ | ✓ |
| workflow_id | text | | ✗ | ✓ |

4. Click **Save** to create the table

5. Add a foreign key constraint to the `lead_id` column:
   - Go to the **SQL Editor** in your Supabase dashboard
   - Run the following SQL:

```sql
ALTER TABLE activities ADD CONSTRAINT activities_lead_id_fkey 
    FOREIGN KEY (lead_id) REFERENCES leads(id) ON DELETE CASCADE;
```

6. Create indexes for the `activities` table:
   - Go to the **SQL Editor** in your Supabase dashboard
   - Run the following SQL statements one by one:

```sql
CREATE INDEX idx_activities_lead_id ON activities(lead_id);
CREATE INDEX idx_activities_activity_type ON activities(activity_type);
CREATE INDEX idx_activities_created_at ON activities(created_at);
```

## Step 5: Create the Offers Table

1. Navigate to the **Table Editor** in your Supabase dashboard
2. Click **Create a new table**
3. Enter the following details:
   - **Name**: `offers`
   - **Enable Row Level Security (RLS)**: Checked
   - **Columns**:

| Name | Type | Default Value | Primary | Nullable |
|------|------|--------------|---------|----------|
| id | uuid | uuid_generate_v4() | ✓ | ✗ |
| lead_id | uuid | | ✗ | ✗ |
| property_address | text | | ✗ | ✗ |
| offer_amount | decimal(12,2) | | ✗ | ✗ |
| mao_amount | decimal(12,2) | | ✗ | ✓ |
| arv | decimal(12,2) | | ✗ | ✓ |
| repair_estimate | decimal(12,2) | | ✗ | ✓ |
| holding_costs | decimal(12,2) | | ✗ | ✓ |
| closing_costs | decimal(12,2) | | ✗ | ✓ |
| profit_margin | decimal(5,2) | | ✗ | ✓ |
| offer_date | timestamptz | now() | ✗ | ✓ |
| expiration_date | timestamptz | | ✗ | ✓ |
| status | text | 'draft' | ✗ | ✓ |
| notes | text | | ✗ | ✓ |
| created_at | timestamptz | now() | ✗ | ✓ |
| updated_at | timestamptz | now() | ✗ | ✓ |
| metadata | jsonb | '{}' | ✗ | ✓ |
| offer_document_url | text | | ✗ | ✓ |

4. Click **Save** to create the table

5. Add a foreign key constraint to the `lead_id` column:
   - Go to the **SQL Editor** in your Supabase dashboard
   - Run the following SQL:

```sql
ALTER TABLE offers ADD CONSTRAINT offers_lead_id_fkey 
    FOREIGN KEY (lead_id) REFERENCES leads(id) ON DELETE CASCADE;
```

6. Create indexes for the `offers` table:
   - Go to the **SQL Editor** in your Supabase dashboard
   - Run the following SQL statements one by one:

```sql
CREATE INDEX idx_offers_lead_id ON offers(lead_id);
CREATE INDEX idx_offers_status ON offers(status);
CREATE INDEX idx_offers_offer_date ON offers(offer_date);
CREATE INDEX idx_offers_expiration_date ON offers(expiration_date);
```

## Step 6: Create the System Tables

### System Config Table

1. Navigate to the **Table Editor** in your Supabase dashboard
2. Click **Create a new table**
3. Enter the following details:
   - **Name**: `system_config`
   - **Enable Row Level Security (RLS)**: Checked
   - **Columns**:

| Name | Type | Default Value | Primary | Nullable |
|------|------|--------------|---------|----------|
| key | text | | ✓ | ✗ |
| value | text | | ✗ | ✗ |
| description | text | | ✗ | ✓ |
| updated_at | timestamptz | now() | ✗ | ✓ |
| updated_by | text | | ✗ | ✓ |

4. Click **Save** to create the table

5. Insert initial configuration values:
   - Go to the **Table Editor** in your Supabase dashboard
   - Select the `system_config` table
   - Click **Insert Row** and add the following rows one by one:

| key | value | description |
|-----|-------|-------------|
| mao_profit_margin | 0.15 | Default profit margin for MAO calculations |
| mao_holding_cost_percent | 0.02 | Default holding cost percentage for MAO calculations |
| mao_closing_cost_percent | 0.03 | Default closing cost percentage for MAO calculations |
| lead_auto_tier_enabled | true | Enable automatic lead tiering |
| webhook_validation_enabled | true | Enable webhook payload validation |
| openai_model | gpt-4o | Default OpenAI model to use for AI tasks |

### Agent Errors Table

1. Navigate to the **Table Editor** in your Supabase dashboard
2. Click **Create a new table**
3. Enter the following details:
   - **Name**: `agent_errors`
   - **Enable Row Level Security (RLS)**: Checked
   - **Columns**:

| Name | Type | Default Value | Primary | Nullable |
|------|------|--------------|---------|----------|
| id | uuid | uuid_generate_v4() | ✓ | ✗ |
| workflow_name | text | | ✗ | ✓ |
| node_name | text | | ✗ | ✓ |
| error_message | text | | ✗ | ✗ |
| timestamp | timestamptz | now() | ✗ | ✓ |
| input_payload | jsonb | | ✗ | ✓ |
| stack_trace | text | | ✗ | ✓ |

4. Click **Save** to create the table

5. Create indexes for the `agent_errors` table:
   - Go to the **SQL Editor** in your Supabase dashboard
   - Run the following SQL statements one by one:

```sql
CREATE INDEX idx_agent_errors_timestamp ON agent_errors(timestamp);
CREATE INDEX idx_agent_errors_workflow_name ON agent_errors(workflow_name);
```

### Webhook Logs Table

1. Navigate to the **Table Editor** in your Supabase dashboard
2. Click **Create a new table**
3. Enter the following details:
   - **Name**: `webhook_logs`
   - **Enable Row Level Security (RLS)**: Checked
   - **Columns**:

| Name | Type | Default Value | Primary | Nullable |
|------|------|--------------|---------|----------|
| id | uuid | uuid_generate_v4() | ✓ | ✗ |
| source | text | | ✗ | ✗ |
| event_type | text | | ✗ | ✗ |
| payload | jsonb | | ✗ | ✗ |
| processed | boolean | false | ✗ | ✓ |
| created_at | timestamptz | now() | ✗ | ✓ |
| processed_at | timestamptz | | ✗ | ✓ |
| error_message | text | | ✗ | ✓ |

4. Click **Save** to create the table

5. Create indexes for the `webhook_logs` table:
   - Go to the **SQL Editor** in your Supabase dashboard
   - Run the following SQL statements one by one:

```sql
CREATE INDEX idx_webhook_logs_source ON webhook_logs(source);
CREATE INDEX idx_webhook_logs_event_type ON webhook_logs(event_type);
CREATE INDEX idx_webhook_logs_created_at ON webhook_logs(created_at);
CREATE INDEX idx_webhook_logs_processed ON webhook_logs(processed);
```

## Step 7: Create Updated_at Triggers

1. Go to the **SQL Editor** in your Supabase dashboard
2. Run the following SQL to create the trigger function:

```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

3. Create triggers for each table:

```sql
CREATE TRIGGER update_kb_documents_updated_at
BEFORE UPDATE ON kb_documents
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_leads_updated_at
BEFORE UPDATE ON leads
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_offers_updated_at
BEFORE UPDATE ON offers
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
```

## Step 8: Create KPI Views

1. Go to the **SQL Editor** in your Supabase dashboard
2. Run the following SQL statements one by one:

```sql
-- Create active tier1 leads count view
CREATE OR REPLACE VIEW vw_active_tier1_leads AS
SELECT COUNT(*) AS count FROM leads WHERE status = 'active' AND tier = 1;

-- Create offers sent today view
CREATE OR REPLACE VIEW vw_offers_sent_today AS
SELECT COUNT(*) AS count FROM offers WHERE offer_date >= CURRENT_DATE;

-- Create deals won MTD view
CREATE OR REPLACE VIEW vw_deals_won_mtd AS
SELECT COUNT(*) AS count FROM offers 
WHERE status = 'accepted' AND offer_date >= DATE_TRUNC('month', CURRENT_DATE);

-- Create OpenAI spend today view
CREATE OR REPLACE VIEW vw_openai_spend_today AS
SELECT COALESCE(SUM(CAST(metadata->>'cost' AS DECIMAL)), 0) AS amount 
FROM activities 
WHERE activity_type = 'openai_call' AND created_at >= CURRENT_DATE;

-- Create combined KPI view
CREATE OR REPLACE VIEW vw_kpi_summary AS
SELECT 
    (SELECT count FROM vw_active_tier1_leads) AS active_tier1_leads,
    (SELECT count FROM vw_offers_sent_today) AS offers_sent_today,
    (SELECT count FROM vw_deals_won_mtd) AS deals_won_mtd,
    (SELECT amount FROM vw_openai_spend_today) AS openai_spend_today;
```

## Step 9: Configure Row Level Security (RLS) Policies

1. Go to the **Authentication** section in your Supabase dashboard
2. Click on **Policies** in the sidebar
3. For each table (`kb_documents`, `leads`, `activities`, `offers`, `system_config`, `agent_errors`, `webhook_logs`), create a read-only policy:
   - Click on the table name
   - Click **New Policy**
   - Select **Create a policy from scratch**
   - Policy name: `[table_name]_readonly` (e.g., `kb_documents_readonly`)
   - For the policy definition, select:
     - **Operation**: SELECT
     - **Using expression**: `true`
     - **With check expression**: Leave empty
   - Click **Save** to create the policy

## Step 10: Create Read-Only Role (Optional)

If you need a read-only role for your application:

1. Go to the **SQL Editor** in your Supabase dashboard
2. Run the following SQL:

```sql
-- Check if role exists before creating
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM pg_roles WHERE rolname = 'ai_readonly'
    ) THEN
        CREATE ROLE ai_readonly;
    END IF;
END
$$;

-- Grant SELECT permissions
GRANT SELECT ON ALL TABLES IN SCHEMA public TO ai_readonly;
```

## Verification

After completing all the steps, verify that your tables have been created correctly:

1. Navigate to the **Table Editor** in your Supabase dashboard
2. You should see all the tables listed: `kb_documents`, `leads`, `activities`, `offers`, `system_config`, `agent_errors`, and `webhook_logs`
3. Click on each table to verify that the columns and constraints are set up correctly
4. Go to the **SQL Editor** and run a simple query to test:

```sql
SELECT * FROM system_config LIMIT 5;
```

This should return the configuration values you inserted earlier.

## Troubleshooting

If you encounter any issues during the manual migration process:

1. **Permission errors**: Ensure you have admin privileges for your Supabase project
2. **SQL errors**: Check the syntax of your SQL statements and ensure they are compatible with PostgreSQL 14 (which Supabase uses)
3. **Missing extensions**: Verify that the required extensions (`uuid-ossp` and `vector`) are enabled
4. **Foreign key constraints**: Ensure that tables are created in the correct order (tables with foreign keys should be created after the tables they reference)

For additional help, refer to the [Supabase documentation](https://supabase.com/docs) or contact Supabase support.
