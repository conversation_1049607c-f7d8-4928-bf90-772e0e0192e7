# GoHighLevel Webhook Registration

This document explains how to register your webhook URL with GoHighLevel using the provided script.

## Prerequisites

Before registering your webhook, ensure you have:

1. A GoHighLevel API key
2. A publicly accessible URL for your webhook endpoint
3. Node.js installed (version 18 or higher)

## Configuration

The webhook registration script uses the following environment variables:

- `GHL_API_KEY` - Your GoHighLevel API key (required)
- `WEBHOOK_URL` - Your webhook URL (optional, can be provided via command line)

These can be set in your `.env` file or provided directly when running the script.

## Usage

### Basic Usage

To register your webhook URL with default event subscriptions:

```bash
npm run register-webhook -- --url https://your-domain.com/webhooks/ghl
```

Or if you've set the `WEBHOOK_URL` in your `.env` file:

```bash
npm run register-webhook
```

### Advanced Options

The script supports several command-line options:

- `-u, --url <url>` - Webhook URL to register
- `-e, --events <events>` - Comma-separated list of events to subscribe to
- `-t, --test` - Test the webhook without registering it
- `-v, --verbose` - Enable verbose logging

Examples:

```bash
# Register specific events only
npm run register-webhook -- --url https://your-domain.com/webhooks/ghl --events contact.created,contact.updated

# Test the webhook without registering
npm run register-webhook -- --url https://your-domain.com/webhooks/ghl --test

# Enable verbose logging
npm run register-webhook -- --url https://your-domain.com/webhooks/ghl --verbose
```

## Default Events

By default, the script registers the following events:

- `contact.created` - Triggered when a new contact is created
- `contact.updated` - Triggered when a contact is updated
- `contact.tag.added` - Triggered when a tag is added to a contact
- `contact.tag.removed` - Triggered when a tag is removed from a contact
- `opportunity.created` - Triggered when a new opportunity is created
- `opportunity.status.updated` - Triggered when an opportunity status is updated
- `form.submitted` - Triggered when a form is submitted

## Troubleshooting

If you encounter issues with webhook registration:

1. Ensure your GoHighLevel API key is valid
2. Verify that your webhook URL is publicly accessible
3. Check that your webhook endpoint returns a 200 OK response
4. Run the script with the `--verbose` flag for more detailed logs
5. Use the `--test` flag to test your webhook without registering it

## Security Considerations

- Keep your GoHighLevel API key secure
- Implement proper authentication for your webhook endpoint
- Consider using HTTPS for your webhook URL
- Validate incoming webhook requests using the `Authorization` header
