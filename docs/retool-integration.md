# Integrating Supabase Realtime with Retool

This document explains how to integrate Supabase realtime subscriptions with your Retool dashboard to enable live updates.

## Overview

Supabase provides realtime functionality that allows you to subscribe to changes in your database tables. By integrating this with Retool, you can create dashboards that update in real-time when data changes.

## Prerequisites

Before setting up realtime integration, ensure you have:

1. A Supabase project with realtime enabled
2. A Retool account with access to create and edit apps
3. The Supabase JavaScript client library

## Enabling Realtime in Supabase

By default, Supabase enables realtime for all tables in your database. However, you may need to explicitly enable it for specific tables:

1. Go to your Supabase project dashboard
2. Navigate to Database → Replication
3. Ensure that the tables you want to track are enabled for realtime

Alternatively, you can enable realtime programmatically using our `RealtimeService`:

```javascript
const realtimeService = require('../services/realtime');

// Initialize the realtime service
await realtimeService.initialize();

// Enable realtime for specific tables
await realtimeService.enableRealtimeForTables([
  'leads',
  'activities',
  'offers'
]);
```

## Integrating with Retool

### Method 1: Using Retool's JavaScript Query

The simplest way to integrate Supabase realtime with Retool is to use a JavaScript query:

1. Create a new JavaScript query in your Retool app
2. Add the Supabase JavaScript client:

```javascript
// Initialize Supabase client
const { createClient } = supabase;
const supabaseUrl = {{ global.SUPABASE_URL }};
const supabaseKey = {{ global.SUPABASE_KEY }};
const supabase = createClient(supabaseUrl, supabaseKey);

// Subscribe to changes
const channel = supabase
  .channel('table_changes')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'leads'
  }, (payload) => {
    // Trigger a refresh of your queries
    queries.fetchLeads.trigger();
    // Or update state directly
    setState({ lastUpdate: new Date().toISOString() });
  })
  .subscribe();

// Return a cleanup function
return () => {
  channel.unsubscribe();
};
```

3. Set the query to run on page load
4. Add a cleanup function to unsubscribe when the component unmounts

### Method 2: Using a Custom Webhook

For more complex scenarios, you can use a custom webhook:

1. Create a webhook endpoint in your backend:

```javascript
const express = require('express');
const router = express.Router();
const realtimeService = require('../services/realtime');

// Initialize realtime service
realtimeService.initialize();

// Subscribe to table changes
realtimeService.subscribeToTable('leads', (payload) => {
  // Broadcast to connected clients
  io.emit('leads_update', payload);
});

module.exports = router;
```

2. Use Retool's WebSocket resource to connect to your webhook

### Method 3: Using Retool's Resource Events

Retool supports resource events that can be used to listen for changes:

1. Create a new resource in Retool for Supabase
2. Configure the resource to use the Supabase API
3. Enable resource events for the tables you want to track
4. Use the resource events to trigger refreshes or update state

## Example: Real-time KPI Dashboard

Here's an example of how to create a real-time KPI dashboard in Retool:

1. Create a new JavaScript query to subscribe to changes:

```javascript
const { createClient } = supabase;
const supabaseUrl = {{ global.SUPABASE_URL }};
const supabaseKey = {{ global.SUPABASE_KEY }};
const supabase = createClient(supabaseUrl, supabaseKey);

// Subscribe to changes in the leads table
const leadsChannel = supabase
  .channel('leads_changes')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'leads'
  }, (payload) => {
    // Refresh the leads query
    queries.fetchLeads.trigger();
    // Update last refresh timestamp
    setState({ lastLeadsUpdate: new Date().toISOString() });
  })
  .subscribe();

// Subscribe to changes in the offers table
const offersChannel = supabase
  .channel('offers_changes')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'offers'
  }, (payload) => {
    // Refresh the offers query
    queries.fetchOffers.trigger();
    // Update last refresh timestamp
    setState({ lastOffersUpdate: new Date().toISOString() });
  })
  .subscribe();

// Return a cleanup function
return () => {
  leadsChannel.unsubscribe();
  offersChannel.unsubscribe();
};
```

2. Create SQL queries to fetch the data:

```sql
-- fetchLeads query
SELECT COUNT(*) as active_leads
FROM leads
WHERE status = 'active' AND tier = 1;

-- fetchOffers query
SELECT COUNT(*) as offers_sent_today
FROM offers
WHERE offer_date >= CURRENT_DATE;
```

3. Add KPI components to your dashboard:
   - Add a Number component for active leads
   - Add a Number component for offers sent today
   - Bind the components to the query results
   - Add a Text component to show the last update time

4. Add a refresh button to manually trigger the queries

## Best Practices

1. **Limit Subscriptions**: Only subscribe to tables that need real-time updates to avoid unnecessary overhead.

2. **Handle Reconnections**: Implement reconnection logic to handle network interruptions.

3. **Filter Events**: Use filters to only receive relevant events:

```javascript
supabase
  .channel('filtered_changes')
  .on('postgres_changes', {
    event: 'UPDATE',
    schema: 'public',
    table: 'leads',
    filter: 'tier=eq.1'
  }, (payload) => {
    // Only triggered for tier 1 leads
    queries.fetchTier1Leads.trigger();
  })
  .subscribe();
```

4. **Debounce Updates**: If multiple changes happen in quick succession, debounce your refresh logic:

```javascript
let debounceTimer;
supabase
  .channel('debounced_changes')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'leads'
  }, (payload) => {
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => {
      queries.fetchLeads.trigger();
    }, 500); // Wait 500ms before refreshing
  })
  .subscribe();
```

5. **Cleanup Subscriptions**: Always unsubscribe when components unmount to prevent memory leaks.

## Troubleshooting

### Common Issues

1. **No Events Received**:
   - Verify that realtime is enabled for your tables
   - Check that your Supabase API key has the necessary permissions
   - Ensure your subscription is correctly configured

2. **Performance Issues**:
   - Limit the number of subscriptions
   - Use filters to reduce the number of events
   - Implement debouncing for frequent updates

3. **Connection Drops**:
   - Implement reconnection logic
   - Check for network issues
   - Verify Supabase service status

### Testing Realtime Subscriptions

You can use our test client to verify that realtime subscriptions are working:

```bash
node src/clients/realtime-client.js --table leads --event INSERT
```

This will subscribe to INSERT events on the leads table and log them to the console.

## Additional Resources

- [Supabase Realtime Documentation](https://supabase.io/docs/guides/realtime)
- [Retool JavaScript Query Documentation](https://docs.retool.com/docs/javascript-query)
- [Retool WebSocket Resource Documentation](https://docs.retool.com/docs/websocket-resource)
