# MAO Calculator Integration Documentation

This document provides a comprehensive overview of how the Maximum Allowable Offer (MAO) calculator integrates with the offer template and follow-up scheduler to create a complete workflow for property acquisition.

## System Components

The MAO workflow consists of three main components:

1. **MAO Calculator** (`agents/mao_calculator.py`): Calculates the maximum allowable offer for a property based on ARV, repair costs, and other factors.
2. **MAO Offer Template** (`prompt_templates/mao_offer.md`): A template for generating property offers using the MAO calculation results.
3. **Follow-up Scheduler** (`schedulers/follow_up_scheduler.py`): Manages the timing and execution of follow-up communications and disposition actions.

## Integration Flow

The following diagram illustrates how these components work together:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Property Data  │     │   Comps Data    │     │  Knowledge Base │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                        MAO Calculator                            │
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────┐  │
│  │ Repair Estimate │    │ MAO Calculation │    │ Confidence  │  │
│  └────────┬────────┘    └────────┬────────┘    │   Score     │  │
│           │                      │             └─────────────┘  │
│           └──────────────────────┘                    │         │
└─────────────────────────┬───────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Offer Generation                            │
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────┐  │
│  │  MAO Result     │    │  Offer Details  │    │  Template   │  │
│  └────────┬────────┘    └────────┬────────┘    │  Variables  │  │
│           │                      │             └─────────────┘  │
│           └──────────────────────┘                    │         │
└─────────────────────────┬───────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Supabase Storage                             │
└─────────────────────────┬───────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Follow-up Scheduler                           │
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────┐  │
│  │ Disposition     │    │ Follow-up       │    │ Time Window │  │
│  │ Stages          │    │ Stages          │    │ Enforcement │  │
│  └────────┬────────┘    └────────┬────────┘    └─────────────┘  │
│           │                      │                    │         │
│           └──────────────────────┘                    │         │
└─────────────────────────┬───────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Communication                               │
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────┐  │
│  │ DispoBot        │    │ FollowUpBot     │    │ GHL Client  │  │
│  └────────┬────────┘    └────────┬────────┘    └─────────────┘  │
│           │                      │                    │         │
│           └──────────────────────┘                    │         │
└─────────────────────────────────────────────────────────────────┘
```

## Detailed Integration Process

### 1. MAO Calculation

The process begins with the MAO calculator, which takes property data and comps data as input:

```python
# Example usage of MAO calculator
from agents.mao_calculator import MAOCalculator

# Initialize the calculator
calculator = MAOCalculator()

# Property data example
property_data = {
    "id": "prop_123",
    "address": "123 Main St, Anytown, USA",
    "sqft": 1500,
    "condition": "fair",
    "year_built": 1985,
    "has_pool": False,
    "lot_size": 8000,
    "stories": 1
}

# ARV from comping workflow
arv = 250000

# Calculate MAO
mao_result = calculator.calculate_mao(property_data, arv)

# Store the result in Supabase
calculator.store_mao_result(mao_result)
```

The MAO calculator performs the following steps:
1. Retrieves calculation parameters from the knowledge base
2. Estimates repair costs based on property condition, size, age, and features
3. Calculates holding costs, closing costs, and contingency
4. Determines the minimum required profit
5. Calculates the MAO as: ARV - total costs - minimum profit
6. Stores the result in Supabase for dashboard access

### 2. Offer Generation

Once the MAO is calculated, an offer can be generated using the MAO offer template:

```python
# Example of generating an offer
offer_details = calculator.generate_offer_from_comps(property_data, comps_result)

# The offer_details contains all the information needed for the template
print(f"MAO: ${offer_details['mao']}")
print(f"ARV: ${offer_details['arv']}")
print(f"Repair Cost: ${offer_details['repair_cost']}")
print(f"Confidence Score: {offer_details['confidence_score']}")
```

The offer generation process:
1. Uses the MAO calculation result
2. Adds offer-specific details like expiration date
3. Calculates a confidence score based on comps quality and quantity
4. Prepares a structured offer details dictionary

The MAO offer template (`prompt_templates/mao_offer.md`) is then populated with values from the offer details:

```
# Property Offer: ${address}, ${city}, ${state} ${zip}

Dear Property Owner,

Based on our comprehensive analysis of your property and recent comparable sales in your area, we are pleased to present you with a cash offer of:

## **$${mao}**

This offer is based on the following analysis:

- **After Repair Value (ARV)**: $${arv}
- **Estimated Repair Costs**: $${repair_cost}
...
```

### 3. Follow-up Scheduling

The follow-up scheduler manages when and how to communicate with leads about offers:

```python
# Example of scheduling follow-ups
from schedulers.follow_up_scheduler import run_scheduler

# Run the scheduler with limits
result = run_scheduler(max_leads=5, max_properties=5)

# Check the results
print(f"Leads processed: {result['leads_processed']}")
print(f"Properties processed: {result['properties_processed']}")
```

The follow-up scheduler:
1. Checks if the current time is within the allowed communication window
2. Retrieves pending leads and properties due for follow-up
3. Determines the appropriate follow-up or disposition stage
4. Runs the appropriate bot (FollowUpBot or DispoBot) to handle communication
5. Updates the follow-up or disposition stage
6. Adds random delays between actions to appear more natural

## Disposition Workflow

The disposition workflow manages the process of making offers and following up:

1. **Initial Offer (Day 0)**:
   - DispoBot sends the initial offer using the MAO offer template
   - The offer includes the MAO, ARV, repair costs, and other details
   - The offer has an expiration date (typically 7 days)

2. **First Follow-up (Day 3)**:
   - DispoBot sends a follow-up message to check if the offer was received
   - Reminds the seller of the offer details and benefits

3. **Second Follow-up (Day 7)**:
   - DispoBot sends another follow-up near the expiration date
   - Creates urgency by mentioning the expiration date

4. **Final Follow-up (Day 14)**:
   - DispoBot sends a final follow-up after the expiration date
   - May include a revised offer or extension if appropriate

## Lead Follow-up Workflow

The lead follow-up workflow manages general communication with leads:

1. **Initial Follow-up (Day 2)**:
   - FollowUpBot sends an initial follow-up message
   - High priority communication

2. **Reminder Follow-up (Day 5)**:
   - FollowUpBot sends a reminder message
   - Medium priority communication

3. **Final Follow-up (Day 10)**:
   - FollowUpBot sends a final follow-up message
   - Low priority communication
   - Adds a note to the lead record that no further automated follow-ups will be sent

## Integration with External Systems

### Supabase Integration

The MAO calculator and follow-up scheduler both integrate with Supabase:

1. **MAO Calculator**:
   - Stores MAO calculation results in Supabase
   - Uses Supabase edge functions for data storage

2. **Follow-up Scheduler**:
   - Retrieves leads and properties from Supabase
   - Updates follow-up and disposition stages in Supabase
   - Logs agent runs in Supabase

### Go High Level (GHL) Integration

The follow-up scheduler integrates with Go High Level (GHL) for CRM functionality:

1. **Adding Notes**:
   - Adds notes to contact records after final follow-ups
   - Uses the GHL client for API communication

2. **Contact Management**:
   - Uses GHL contact IDs to associate leads with contacts in GHL
   - Ensures consistent communication across platforms

## Typical Usage Patterns

### Pattern 1: Full Workflow

The most common usage pattern is the full workflow, which includes:

1. Property data collection
2. Comping to determine ARV
3. MAO calculation
4. Offer generation
5. Initial offer communication
6. Scheduled follow-ups
7. Disposition management

Example code for the full workflow:

```python
# 1. Collect property data
property_data = get_property_data(property_id)

# 2. Run comping workflow
comps_result = run_comping_workflow(property_data)

# 3. Initialize MAO calculator
calculator = MAOCalculator()

# 4. Generate offer
offer_details = calculator.generate_offer_from_comps(property_data, comps_result)

# 5. Store the offer in Supabase (done automatically in generate_offer_from_comps)

# 6. The follow-up scheduler will handle the rest automatically
# It will pick up the property for disposition actions based on the schedule
```

### Pattern 2: Manual MAO Calculation

For situations where you need to calculate MAO without the full workflow:

```python
# Initialize calculator
calculator = MAOCalculator()

# Calculate MAO with known ARV
arv = 250000
mao_result = calculator.calculate_mao(property_data, arv)

# Print the result
print(f"MAO: ${mao_result['mao']}")
print(f"Repair Cost: ${mao_result['calculation_details']['repair_cost']}")
print(f"Total Costs: ${mao_result['calculation_details']['total_costs']}")
```

### Pattern 3: Dry Run Scheduling

For testing the follow-up scheduler without sending actual communications:

```python
# Run the scheduler in dry run mode
result = run_scheduler(max_leads=5, max_properties=5, dry_run=True)

# Check what would have been processed
print(f"Leads that would be processed: {result['leads_processed']}")
print(f"Properties that would be processed: {result['properties_processed']}")
```

## Recommendations for Optimization

Based on the analysis of the components, here are recommendations for optimizing the integration:

### 1. Enhanced MAO Calculator

1. **Regional Cost Adjustments**:
   ```python
   # Add regional cost factors to calculation parameters
   "regional_cost_factors": {
       "west_coast": 1.3,
       "east_coast": 1.2,
       "midwest": 0.9,
       "south": 0.85
   }
   ```

2. **More Granular Condition Assessment**:
   ```python
   # Expand condition categories
   "repair_cost_factors": {
       "very_poor": 60,
       "poor": 40,
       "fair": 25,
       "good": 15,
       "excellent": 5,
       "new_construction": 2
   }
   ```

3. **Improved Confidence Scoring**:
   ```python
   # More sophisticated confidence scoring
   def _calculate_confidence_score(self, comps_result, mao_result):
       # Base score
       score = 0.5
       
       # Comp quantity factor (0-0.2)
       comps_count = len(comps_result.get("comps", []))
       comp_quantity_factor = min(0.2, comps_count * 0.04)
       
       # Comp quality factor (0-0.3)
       comp_scores = [comp.get("score", 0) for comp in comps_result.get("comps", [])]
       avg_comp_score = sum(comp_scores) / len(comp_scores) if comp_scores else 0
       comp_quality_factor = avg_comp_score * 0.3
       
       # Repair estimate confidence (0-0.3)
       repair_confidence = self._calculate_repair_confidence(mao_result)
       
       # ARV confidence (0-0.2)
       arv_confidence = comps_result.get("arv_result", {}).get("confidence", 0.5) * 0.2
       
       # Calculate final score
       score = comp_quantity_factor + comp_quality_factor + repair_confidence + arv_confidence
       
       # Ensure score is between 0 and 1
       return max(0.0, min(1.0, score))
   ```

### 2. Enhanced Offer Template

1. **Personalization**:
   ```markdown
   Dear ${first_name},

   Thank you for considering selling your property at ${address}. Based on our comprehensive analysis...
   ```

2. **Confidence Indicator**:
   ```markdown
   ## Our Confidence Level: ${confidence_level}

   Based on the quality and quantity of comparable properties in your area, we have a ${confidence_text} level of confidence in this offer.
   ```

3. **Detailed Repair Breakdown**:
   ```markdown
   ## Repair Cost Breakdown:
   
   - Foundation & Structure: $${foundation_cost}
   - Roof & Exterior: $${exterior_cost}
   - Plumbing & Electrical: $${systems_cost}
   - Interior Finishes: $${interior_cost}
   - Landscaping & Exterior: $${landscaping_cost}
   ```

### 3. Enhanced Follow-up Scheduler

1. **Dynamic Timing Based on Engagement**:
   ```python
   def calculate_next_follow_up_date(lead_id, current_stage, engagement_level):
       """Calculate next follow-up date based on engagement level."""
       base_days = FOLLOW_UP_STAGES[current_stage]["days"]
       
       # Adjust timing based on engagement
       if engagement_level == "high":
           # Engaged leads get faster follow-up
           return base_days * 0.7
       elif engagement_level == "low":
           # Less engaged leads get slower follow-up
           return base_days * 1.3
       else:
           # Default timing
           return base_days
   ```

2. **Prioritization Based on Property Value**:
   ```python
   def get_pending_dispositions(max_properties=10):
       """Get properties prioritized by value."""
       # Query for properties
       res = supabase.table("properties")\
           .select("id, lead_id, address, status, last_disposition_action, disposition_stage, created_at, estimated_value")\
           .in_("status", ["active", "pending", "analyzing"])\
           .lte("last_disposition_action", default_cutoff)\
           .order("estimated_value", desc=True)\  # Prioritize higher value properties
           .limit(max_properties)\
           .execute()
       
       # Process results as before
       # ...
   ```

3. **A/B Testing Framework**:
   ```python
   def run_follow_up_bot(lead_id, follow_up_stage, follow_up_count, lead_data=None):
       """Run follow-up bot with A/B testing."""
       # Determine which variant to use
       variant = determine_ab_test_variant(lead_id)
       
       # Initialize bot with variant
       bot = FollowUpBot(
           agent_name="follow_up_bot", 
           lead_id=lead_id, 
           entity=lead_id,
           variant=variant
       )
       
       # Run the bot
       result = bot.run()
       
       # Log the run with variant information
       log_agent_run("follow_up_bot", lead_id, lead_id, result, variant=variant)
       
       # Update follow-up count and timestamp
       # ...
       
       # Track variant performance
       track_variant_performance(lead_id, variant, result)
       
       return "error" not in result.lower()
   ```

## Integration Testing

To test the integration between components, you can use the following approach:

```python
def test_mao_integration():
    """Test the integration between MAO calculator, offer template, and follow-up scheduler."""
    # 1. Create test property data
    property_data = {
        "id": "test_prop_123",
        "address": "123 Test St, Testville, TS 12345",
        "sqft": 1500,
        "condition": "fair",
        "year_built": 1985,
        "has_pool": False,
        "lot_size": 8000,
        "stories": 1
    }
    
    # 2. Create test comps data
    comps_result = {
        "comps": [
            {"address": "125 Test St", "sale_price": 240000, "score": 0.8},
            {"address": "127 Test St", "sale_price": 250000, "score": 0.9},
            {"address": "129 Test St", "sale_price": 260000, "score": 0.7}
        ],
        "arv_result": {
            "arv": 250000,
            "confidence": 0.8
        }
    }
    
    # 3. Initialize MAO calculator
    calculator = MAOCalculator()
    
    # 4. Generate offer
    offer_details = calculator.generate_offer_from_comps(property_data, comps_result)
    
    # 5. Verify offer details
    assert offer_details["success"] == True
    assert offer_details["property_id"] == "test_prop_123"
    assert offer_details["arv"] == 250000
    assert offer_details["mao"] > 0
    assert offer_details["confidence_score"] > 0
    
    # 6. Run scheduler in dry run mode
    scheduler_result = run_scheduler(max_leads=1, max_properties=1, dry_run=True)
    
    # 7. Verify scheduler result
    assert scheduler_result["success"] == True
    
    print("Integration test passed!")
```

## Conclusion

The integration between the MAO calculator, offer template, and follow-up scheduler creates a powerful workflow for property acquisition. By understanding how these components work together, you can optimize the process for better results.

Key takeaways:
1. The MAO calculator provides accurate offer calculations based on property data and comps
2. The offer template communicates the offer effectively to property owners
3. The follow-up scheduler ensures timely and appropriate follow-up communications
4. The entire workflow is automated and integrated with external systems like Supabase and GHL

By implementing the recommended optimizations, you can further enhance the effectiveness and efficiency of the workflow.
