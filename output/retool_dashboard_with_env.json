{"pages": [{"name": "Dashboard", "layout": {"direction": "vertical", "spacing": 16, "padding": 16}, "elements": [{"type": "Container", "layout": {"direction": "horizontal", "spacing": 16, "alignItems": "center"}, "elements": [{"type": "Image", "url": "https://placeholder.com/wp-content/uploads/2018/10/placeholder.com-logo1.png", "height": 40, "width": 150, "alt": "Company Logo"}, {"type": "Text", "text": "Last refreshed: {{ formatDate(new Date(), 'MMM D, YYYY h:mm:ss a') }}", "style": {"fontSize": "12px", "color": "#666"}}, {"type": "Spacer", "flex": 1}, {"type": "<PERSON><PERSON>", "text": "Refresh Dashboard", "icon": "refresh", "onClick": [{"action": "run<PERSON><PERSON><PERSON>", "queryName": "get_kpis"}, {"action": "run<PERSON><PERSON><PERSON>", "queryName": "mock_workflows"}, {"action": "run<PERSON><PERSON><PERSON>", "queryName": "get_errors"}, {"action": "run<PERSON><PERSON><PERSON>", "queryName": "get_config"}, {"action": "showNotification", "notificationType": "info", "title": "Refreshing", "message": "Dashboard is refreshing..."}]}]}, {"type": "Container", "layout": {"direction": "horizontal", "spacing": 16}, "elements": [{"type": "NumberBox", "label": "Active Tier 1 Leads", "queryName": "get_kpis", "dataKey": "active_tier1_leads", "style": {"backgroundColor": "{{ get_kpis.data[0].active_tier1_leads < 5 ? '#ffebee' : get_kpis.data[0].active_tier1_leads < 10 ? '#fff8e1' : '#e8f5e9' }}", "borderRadius": "4px", "padding": "16px"}, "tooltip": "Number of active leads classified as Tier 1 (high priority). Target: 10+ leads."}, {"type": "NumberBox", "label": "Offers Sent Today", "queryName": "get_kpis", "dataKey": "offers_sent_today", "style": {"backgroundColor": "{{ get_kpis.data[0].offers_sent_today === 0 ? '#ffebee' : get_kpis.data[0].offers_sent_today < 3 ? '#fff8e1' : '#e8f5e9' }}", "borderRadius": "4px", "padding": "16px"}, "tooltip": "Number of property offers sent since midnight UTC. Daily target: 3+ offers."}, {"type": "NumberBox", "label": "Deals Won MTD", "queryName": "get_kpis", "dataKey": "deals_won_mtd", "style": {"backgroundColor": "{{ get_kpis.data[0].deals_won_mtd < 1 ? '#ffebee' : get_kpis.data[0].deals_won_mtd < 3 ? '#fff8e1' : '#e8f5e9' }}", "borderRadius": "4px", "padding": "16px"}, "tooltip": "Number of deals successfully closed month-to-date. Monthly target: 5+ deals."}, {"type": "NumberBox", "label": "OpenAI Spend Today", "queryName": "get_kpis", "dataKey": "openai_spend_today", "format": "currency", "style": {"backgroundColor": "{{ get_kpis.data[0].openai_spend_today > 50 ? '#ffebee' : get_kpis.data[0].openai_spend_today > 30 ? '#fff8e1' : '#e8f5e9' }}", "borderRadius": "4px", "padding": "16px"}, "tooltip": "Total amount spent on OpenAI API calls since midnight UTC. Daily budget: $50."}]}, {"type": "Container", "layout": {"direction": "horizontal", "spacing": 16, "alignItems": "center"}, "elements": [{"type": "Heading", "text": "Workflow Monitor"}, {"type": "Spacer", "flex": 1}, {"type": "<PERSON><PERSON>", "text": "Export CSV", "icon": "download", "size": "small", "onClick": [{"action": "exportData", "data": "{{ mock_workflows.data }}", "fileName": "workflow_monitor_{{ formatDate(new Date(), 'YYYY-MM-DD') }}.csv", "fileType": "csv"}]}]}, {"type": "Table", "queryName": "mock_workflows", "columns": [{"Header": "Workflow", "accessor": "workflow_name"}, {"Header": "Execution ID", "accessor": "execution_id"}, {"Header": "Status", "accessor": "status", "Cell": "{{ cell.value === 'Success' ? '✅ Success' : cell.value === 'Pending' ? '⏳ Pending' : '❌ ' + cell.value }}"}, {"Header": "Started At", "accessor": "started_at", "type": "datetime"}, {"Header": "Duration (ms)", "accessor": "duration_ms"}], "enablePagination": true, "enableSorting": true, "enableFiltering": true, "globalSearch": true, "showLoadingState": true, "onQueryFailed": [{"action": "showNotification", "notificationType": "error", "title": "Workflow Fetch Failed", "message": "{{error.message}}"}]}, {"type": "Container", "layout": {"direction": "horizontal", "spacing": 16, "alignItems": "center"}, "elements": [{"type": "Heading", "text": "Agent <PERSON><PERSON><PERSON>"}, {"type": "Spacer", "flex": 1}, {"type": "<PERSON><PERSON>", "text": "Export CSV", "icon": "download", "size": "small", "onClick": [{"action": "exportData", "data": "{{ get_errors.data }}", "fileName": "agent_errors_{{ formatDate(new Date(), 'YYYY-MM-DD') }}.csv", "fileType": "csv"}]}]}, {"type": "Table", "queryName": "get_errors", "columns": [{"Header": "Workflow", "accessor": "workflow_name"}, {"Header": "Node", "accessor": "node_name"}, {"Header": "Error", "accessor": "error_message"}, {"Header": "Time", "accessor": "timestamp", "type": "datetime"}, {"Header": "Payload", "accessor": "input_payload", "truncate": true}], "enablePagination": true, "enableSorting": true, "enableFiltering": true, "globalSearch": true, "showLoadingState": true, "onQueryFailed": [{"action": "showNotification", "notificationType": "error", "title": "Error Log Fetch Failed", "message": "{{error.message}}"}]}, {"type": "Container", "layout": {"direction": "horizontal", "spacing": 16, "alignItems": "center"}, "elements": [{"type": "Heading", "text": "Config Overrides"}, {"type": "Spacer", "flex": 1}, {"type": "<PERSON><PERSON>", "text": "Export CSV", "icon": "download", "size": "small", "onClick": [{"action": "exportData", "data": "{{ get_config.data }}", "fileName": "config_overrides_{{ formatDate(new Date(), 'YYYY-MM-DD') }}.csv", "fileType": "csv"}]}]}, {"type": "Table", "queryName": "get_config", "columns": [{"Header": "Key", "accessor": "key", "editable": false}, {"Header": "Value", "accessor": "value", "editable": true}], "enablePagination": true, "enableSorting": true, "enableFiltering": true, "showLoadingState": true}, {"type": "Container", "layout": {"direction": "horizontal", "spacing": 16}, "elements": [{"type": "<PERSON><PERSON>", "text": "Save Config", "onClick": [{"action": "showModal", "modalId": "confirmSaveConfig"}]}]}, {"type": "Heading", "text": "Upload File"}, {"type": "Container", "layout": {"direction": "vertical", "spacing": 16}, "elements": [{"type": "FilePicker", "label": "Choose file", "allowMultiple": false, "acceptedFileTypes": ".csv,.xlsx,.json", "tooltip": "Select a file to upload. Accepted formats: CSV, Excel, JSON."}, {"type": "<PERSON><PERSON>", "text": "Send to Ingest", "disabled": "{{!FilePicker.value}}", "onClick": [{"action": "run<PERSON><PERSON><PERSON>", "queryName": "mock_ingest", "queryParams": {"file": "{{FilePicker.value}}"}}, {"action": "showNotification", "notificationType": "success", "title": "Success", "message": "File sent to ingest endpoint"}]}]}], "modals": [{"id": "confirmSaveConfig", "title": "Confirm Configuration Change", "elements": [{"type": "Text", "text": "Are you sure you want to save the following configuration change?", "style": {"marginBottom": "16px"}}, {"type": "Container", "layout": {"direction": "horizontal", "spacing": 8}, "elements": [{"type": "Text", "text": "Key:", "style": {"fontWeight": "bold"}}, {"type": "Text", "text": "{{ table.selectedRow.key }}"}]}, {"type": "Container", "layout": {"direction": "horizontal", "spacing": 8}, "elements": [{"type": "Text", "text": "New Value:", "style": {"fontWeight": "bold"}}, {"type": "Text", "text": "{{ table.selectedRow.value }}"}]}, {"type": "Container", "layout": {"direction": "horizontal", "spacing": 16, "justifyContent": "flex-end", "marginTop": "24px"}, "elements": [{"type": "<PERSON><PERSON>", "text": "Cancel", "variant": "secondary", "onClick": [{"action": "closeModal", "modalId": "confirmSaveConfig"}]}, {"type": "<PERSON><PERSON>", "text": "Confirm", "variant": "primary", "onClick": [{"action": "run<PERSON><PERSON><PERSON>", "queryName": "update_config"}, {"action": "closeModal", "modalId": "confirmSaveConfig"}, {"action": "showNotification", "notificationType": "success", "title": "Success", "message": "Configuration updated successfully"}, {"action": "run<PERSON><PERSON><PERSON>", "queryName": "get_config"}]}]}]}]}], "queries": [{"name": "get_kpis", "resource": "Supabase", "type": "sql", "query": "SELECT active_tier1_leads, offers_sent_today, deals_won_mtd, openai_spend_today FROM vw_kpi_summary;", "autoRefresh": {"interval": 60}, "onQueryFailed": [{"action": "showNotification", "notificationType": "error", "title": "KPI Fetch Failed", "message": "{{error.message}}"}]}, {"name": "mock_workflows", "resource": "JavaScript", "type": "js", "code": "return [\n  { workflow_name:'tier1_flow', execution_id:'abc123', status:'Success', started_at:'2025-05-28T12:00Z', duration_ms:3500 },\n  { workflow_name:'followup',  execution_id:'def456', status:'Pending', started_at:'2025-05-28T13:00Z', duration_ms:null },\n  { workflow_name:'mao_calc',  execution_id:'ghi789', status:'Failed',  started_at:'2025-05-28T11:30Z', duration_ms:1200 }\n];", "autoRefresh": {"interval": 60}}, {"name": "get_errors", "resource": "Supabase", "type": "sql", "query": "SELECT workflow_name, node_name, error_message, timestamp, input_payload FROM agent_errors ORDER BY timestamp DESC LIMIT 100;", "autoRefresh": {"interval": 60}}, {"name": "get_config", "resource": "Supabase", "type": "sql", "query": "SELECT key, value FROM system_config;", "autoRefresh": {"interval": 60}}, {"name": "update_config", "resource": "Supabase", "type": "sql", "query": "UPDATE system_config SET value = {{ table.selectedRow.value }} WHERE key = {{ table.selectedRow.key }};"}, {"name": "mock_ingest", "resource": "REST_API_GHL", "type": "post", "url": "/ingest", "body": {"file": "{{ file }}"}, "onQueryFailed": [{"action": "showNotification", "notificationType": "error", "title": "Ingest Failed", "message": "{{error.message}}"}]}], "resources": [{"name": "Supabase", "type": "postgres", "connectionString": "{{ PG_CONN_STRING_READONLY }}"}, {"name": "REST_API_GHL", "type": "rest", "baseUrl": "https://api.gohighlevel.com/v1", "headers": {"Authorization": "Bearer {{ GHL_API_KEY }}"}}], "environment": {"PG_CONN_STRING_READONLY": "*****************************************************************************/postgres", "GHL_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2NhdGlvbl9pZCI6ImRETkZMcnVZdHdOQWhvOVVheTJOIiwidmVyc2lvbiI6MSwiaWF0IjoxNzQ1Mzk2NDg1NTc5LCJzdWIiOiJoRmlQN2N0UlhVOVFVYlFOOW44aCJ9.-FFsH_PPwUWULkpTYL-p6AIOVhgp1HaSrY_qH3-pPbk", "RETOOL_API_KEY": "retool_01jsgr8dz3v45xzx21af6h1h3b", "RETOOL_ORG_ID": "advancedinvestor"}, "metadata": {"type": "app", "frameworkVersion": "2.0.0", "autoRefreshInterval": 60}}