{"name": "GHL to Supabase Lead Processing", "nodes": [{"parameters": {"path": "webhook/ghl/lead", "options": {"noResponseBody": false}, "responseMode": "responseNode"}, "id": "webhook-trigger", "name": "GHL Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "ghl-lead-webhook"}, {"parameters": {"jsCode": "// Extract and validate GHL contact data\nconst inputData = $input.all();\nconst webhookData = inputData[0].json;\n\n// Log incoming data for debugging\nconsole.log('Received GHL webhook data:', JSON.stringify(webhookData, null, 2));\n\n// Extract contact information with fallbacks\nconst contact = webhookData.contact || webhookData;\nconst customFields = contact.customFields || [];\nconst tags = contact.tags || [];\n\n// Helper function to get custom field value\nfunction getCustomFieldValue(fields, fieldName) {\n  const field = fields.find(f => f.name === fieldName || f.key === fieldName);\n  return field ? field.value : null;\n}\n\n// Map GHL data to Supabase leads schema\nconst leadData = {\n  // Basic contact information\n  first_name: contact.firstName || contact.first_name || '',\n  last_name: contact.lastName || contact.last_name || '',\n  email: contact.email || '',\n  phone: contact.phone || contact.phoneNumber || '',\n  \n  // Address information\n  address_line_1: contact.address1 || contact.address || '',\n  address_line_2: contact.address2 || '',\n  city: contact.city || '',\n  state: contact.state || '',\n  postal_code: contact.postalCode || contact.zipCode || '',\n  country: contact.country || 'US',\n  \n  // Lead classification and source\n  lead_source: 'gohighlevel',\n  lead_status: 'new',\n  lead_type: getCustomFieldValue(customFields, 'lead_type') || 'prospect',\n  \n  // Investment and financial information\n  investment_experience: getCustomFieldValue(customFields, 'investment_experience') || 'beginner',\n  investment_goals: getCustomFieldValue(customFields, 'investment_goals') || '',\n  risk_tolerance: getCustomFieldValue(customFields, 'risk_tolerance') || 'moderate',\n  annual_income: getCustomFieldValue(customFields, 'annual_income') || null,\n  net_worth: getCustomFieldValue(customFields, 'net_worth') || null,\n  liquid_capital: getCustomFieldValue(customFields, 'liquid_capital') || null,\n  \n  // Communication preferences\n  preferred_contact_method: getCustomFieldValue(customFields, 'preferred_contact_method') || 'email',\n  communication_frequency: getCustomFieldValue(customFields, 'communication_frequency') || 'weekly',\n  \n  // Metadata\n  ghl_contact_id: contact.id || contact.contactId || '',\n  tags: tags.join(', '),\n  notes: contact.notes || '',\n  \n  // Timestamps\n  created_at: new Date().toISOString(),\n  updated_at: new Date().toISOString()\n};\n\n// Validate required fields\nif (!leadData.email && !leadData.phone) {\n  throw new Error('Either email or phone is required for lead creation');\n}\n\n// Log processed data\nconsole.log('Processed lead data:', JSON.stringify(leadData, null, 2));\n\nreturn [{ json: leadData }];"}, "id": "data-processor", "name": "Process GHL Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"operation": "insert", "schema": {"__rl": true, "value": "public", "mode": "list"}, "table": {"__rl": true, "value": "leads", "mode": "list"}, "columns": {"mappingMode": "defineBelow", "value": {"first_name": "={{ $json.first_name }}", "last_name": "={{ $json.last_name }}", "email": "={{ $json.email }}", "phone": "={{ $json.phone }}", "address_line_1": "={{ $json.address_line_1 }}", "address_line_2": "={{ $json.address_line_2 }}", "city": "={{ $json.city }}", "state": "={{ $json.state }}", "postal_code": "={{ $json.postal_code }}", "country": "={{ $json.country }}", "lead_source": "={{ $json.lead_source }}", "lead_status": "={{ $json.lead_status }}", "lead_type": "={{ $json.lead_type }}", "investment_experience": "={{ $json.investment_experience }}", "investment_goals": "={{ $json.investment_goals }}", "risk_tolerance": "={{ $json.risk_tolerance }}", "annual_income": "={{ $json.annual_income }}", "net_worth": "={{ $json.net_worth }}", "liquid_capital": "={{ $json.liquid_capital }}", "preferred_contact_method": "={{ $json.preferred_contact_method }}", "communication_frequency": "={{ $json.communication_frequency }}", "ghl_contact_id": "={{ $json.ghl_contact_id }}", "tags": "={{ $json.tags }}", "notes": "={{ $json.notes }}", "created_at": "={{ $json.created_at }}", "updated_at": "={{ $json.updated_at }}"}, "matchingColumns": [], "schema": [{"id": "first_name", "displayName": "first_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "last_name", "displayName": "last_name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "email", "displayName": "email", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "phone", "displayName": "phone", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}]}, "options": {}}, "id": "supabase-insert", "name": "Insert Lead to Supabase", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 300], "credentials": {"postgres": {"id": "supabase-connection", "name": "Supabase Connection"}}}, {"parameters": {"jsCode": "// Log successful lead creation\nconst leadData = $input.first().json;\nconsole.log('Successfully created lead:', JSON.stringify(leadData, null, 2));\n\n// Return success response for webhook\nreturn [{\n  json: {\n    success: true,\n    message: 'Lead successfully processed and stored',\n    leadId: leadData.id,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"jsCode": "// Handle errors and log them\nconst error = $input.first().json;\nconsole.error('Error processing GHL webhook:', error);\n\n// Return error response\nreturn [{\n  json: {\n    success: false,\n    error: error.message || 'Unknown error occurred',\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "error-handler", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 480]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {"responseCode": 500}}, "id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 480]}], "connections": {"GHL Webhook Trigger": {"main": [[{"node": "Process GHL Data", "type": "main", "index": 0}]]}, "Process GHL Data": {"main": [[{"node": "Insert Lead to Supabase", "type": "main", "index": 0}]]}, "Insert Lead to Supabase": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Success Response": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}, "Error Handler": {"main": [[{"node": "Error Response", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-06-01T07:45:17.000Z", "updatedAt": "2025-06-01T07:45:17.000Z", "id": "ghl-integration", "name": "GHL Integration"}], "triggerCount": 1, "updatedAt": "2025-06-01T07:45:17.000Z", "versionId": "1"}