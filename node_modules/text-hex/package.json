{"name": "text-hex", "version": "1.0.0", "description": "Generate a hex color from the given text", "main": "index.js", "scripts": {"test": "mocha --reporter spec --ui bdd test.js"}, "repository": {"type": "git", "url": "https://github.com/3rd-Eden/text-hex"}, "keywords": ["css", "color", "hex", "text"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/3rd-Eden/text-hex/issues"}, "homepage": "https://github.com/3rd-Eden/text-hex", "devDependencies": {"assume": "2.1.x", "mocha": "5.2.x", "pre-commit": "1.2.x"}}