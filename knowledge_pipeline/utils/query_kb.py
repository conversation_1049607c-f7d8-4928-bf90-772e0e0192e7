import os
import json
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def query_kb(query_text):
    """
    Mock function to query the knowledge base.
    
    Args:
        query_text (str): The query text
        
    Returns:
        str: The knowledge base response
    """
    logger.info(f"Mock knowledge base query: {query_text}")
    
    # Check if the query is for MAO calculation parameters
    if "MAO" in query_text and "parameters" in query_text:
        try:
            # Load mock parameters from file
            with open("mock_data/mao_parameters.json", "r") as f:
                params = json.load(f)
                
            # Convert to string with JSON formatting
            return json.dumps(params, indent=2)
        except Exception as e:
            logger.error(f"Error loading mock parameters: {str(e)}")
            return "Error retrieving parameters"
    
    # Default response for other queries
    return "No specific information available for this query."
