#!/bin/bash
# Contabo VPS setup script for AI-OS

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 AI-OS Contabo VPS Setup${NC}"
echo "================================"

# Check if running as root
if [ "$EUID" -ne 0 ]; then 
    echo -e "${RED}❌ Please run as root: sudo ./contabo-setup.sh${NC}"
    exit 1
fi

# 1. Update system
echo -e "${YELLOW}📦 Updating system packages...${NC}"
apt update && apt upgrade -y

# 2. Install Docker
echo -e "${YELLOW}🐳 Installing Docker...${NC}"
if ! command -v docker &> /dev/null; then
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    systemctl enable docker
    systemctl start docker
    rm get-docker.sh
fi

# 3. Install Docker Compose
echo -e "${YELLOW}🔧 Installing Docker Compose...${NC}"
if ! command -v docker-compose &> /dev/null; then
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
fi

# 4. Install Git
echo -e "${YELLOW}📂 Installing Git...${NC}"
apt install git -y

# 5. Install Caddy for reverse proxy
echo -e "${YELLOW}🌐 Installing Caddy...${NC}"
if ! command -v caddy &> /dev/null; then
    apt install -y debian-keyring debian-archive-keyring apt-transport-https
    curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
    curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | tee /etc/apt/sources.list.d/caddy-stable.list
    apt update
    apt install caddy -y
fi

# 6. Create app directory
echo -e "${YELLOW}📁 Setting up application directory...${NC}"
mkdir -p /opt/AI-OS
cd /opt/AI-OS

# 7. Set up firewall (if ufw is available)
echo -e "${YELLOW}🔒 Configuring firewall...${NC}"
if command -v ufw &> /dev/null; then
    ufw --force enable
    ufw allow ssh
    ufw allow 80
    ufw allow 443
    ufw allow 5002  # AI-OS API
    ufw allow 5678  # n8n
fi

echo -e "${GREEN}✅ Server setup complete!${NC}"
echo ""
echo -e "${YELLOW}📋 Next steps:${NC}"
echo "1. Clone your AI-OS repository"
echo "2. Set up environment variables"  
echo "3. Configure domain and SSL"
echo "4. Deploy with Docker Compose"
echo ""
echo -e "${YELLOW}💡 Run this on your local machine:${NC}"
echo "   rsync -avz --exclude 'node_modules' --exclude '.git' . root@YOUR_VPS_IP:/opt/AI-OS/"