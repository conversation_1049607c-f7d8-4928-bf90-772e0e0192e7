#!/usr/bin/env python3
"""
Comprehensive test suite for enhanced lead processing automation

This test suite covers:
- Enhanced lead scoring and qualification
- Intelligent follow-up automation
- Workflow integration
- API endpoints
- End-to-end processing
"""

import os
import sys
import unittest
import json
import requests
from datetime import datetime
from unittest.mock import Mock, patch

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from agents.workflows.enhanced_lead_processor import (
    EnhancedLeadProcessor,
    LeadScore,
    QualificationResult,
    LeadStatus,
    LeadPriority
)
from agents.workflows.intelligent_followup import (
    IntelligentFollowUp,
    FollowUpChannel,
    FollowUpStage,
    ResponseType
)

class TestEnhancedLeadProcessor(unittest.TestCase):
    """Test cases for enhanced lead processor"""

    def setUp(self):
        """Set up test fixtures"""
        self.sample_lead_data = {
            "lead_id": "test_lead_123",
            "first_name": "<PERSON>",
            "last_name": "<PERSON><PERSON>",
            "email": "<EMAIL>",
            "phone": "+1234567890",
            "property_address": "123 Main St, Atlanta, GA",
            "property_value": 250000,
            "equity": 75000,
            "motivation": "foreclosure, need to sell fast",
            "timeline": "asap",
            "condition": "needs minor repairs",
            "city": "atlanta",
            "state": "GA",
            "zip_code": "30309"
        }

    def test_lead_scoring_calculation(self):
        """Test advanced lead scoring calculation"""
        # Mock the context to return our test data
        with patch('agents.workflows.enhanced_lead_processor.AgentBase.__init__', return_value=None):
            processor = EnhancedLeadProcessor(lead_id="test_lead_123")
            processor.context = {"lead": self.sample_lead_data}
            processor.lead_data = self.sample_lead_data

            # Calculate score
            score = processor.calculate_advanced_score()

            # Assertions
            self.assertIsInstance(score, LeadScore)
            self.assertGreater(score.total_score, 0)
            self.assertLessEqual(score.total_score, 100)
            self.assertIn(score.tier, [1, 2])
            self.assertIsInstance(score.priority, LeadPriority)
            self.assertIsInstance(score.factors, dict)
            self.assertIsInstance(score.recommendations, list)
            self.assertIsInstance(score.next_actions, list)

    def test_high_value_lead_scoring(self):
        """Test scoring for high-value leads"""
        high_value_data = self.sample_lead_data.copy()
        high_value_data.update({
            "property_value": 300000,
            "equity": 100000,
            "motivation": "foreclosure, divorce, urgent",
            "timeline": "immediately",
            "condition": "good condition"
        })

        with patch('agents.workflows.enhanced_lead_processor.AgentBase.__init__', return_value=None):
            processor = EnhancedLeadProcessor(lead_id="test_lead_123")
            processor.context = {"lead": high_value_data}
            processor.lead_data = high_value_data

            score = processor.calculate_advanced_score()

            # High-value lead should score well
            self.assertGreater(score.total_score, 70)
            self.assertEqual(score.tier, 1)
            self.assertIn(score.priority, [LeadPriority.HIGH, LeadPriority.CRITICAL])

    def test_low_value_lead_scoring(self):
        """Test scoring for low-value leads"""
        low_value_data = self.sample_lead_data.copy()
        low_value_data.update({
            "property_value": 80000,
            "equity": 10000,
            "motivation": "just curious, maybe sell",
            "timeline": "next year",
            "condition": "poor condition"
        })

        with patch('agents.workflows.enhanced_lead_processor.AgentBase.__init__', return_value=None):
            processor = EnhancedLeadProcessor(lead_id="test_lead_123")
            processor.context = {"lead": low_value_data}
            processor.lead_data = low_value_data

            score = processor.calculate_advanced_score()

            # Low-value lead should score poorly
            self.assertLess(score.total_score, 50)
            self.assertEqual(score.tier, 2)
            self.assertEqual(score.priority, LeadPriority.LOW)

    def test_lead_qualification(self):
        """Test lead qualification logic"""
        with patch('agents.workflows.enhanced_lead_processor.AgentBase.__init__', return_value=None):
            processor = EnhancedLeadProcessor(lead_id="test_lead_123")
            processor.context = {"lead": self.sample_lead_data}
            processor.lead_data = self.sample_lead_data

            qualification = processor.qualify_lead()

            # Assertions
            self.assertIsInstance(qualification, QualificationResult)
            self.assertIsInstance(qualification.qualified, bool)
            self.assertGreaterEqual(qualification.qualification_score, 0)
            self.assertIsInstance(qualification.disqualification_reasons, list)
            self.assertIsInstance(qualification.qualification_factors, dict)
            self.assertIsInstance(qualification.recommended_actions, list)

    def test_qualified_lead(self):
        """Test qualification for a good lead"""
        qualified_data = self.sample_lead_data.copy()
        qualified_data.update({
            "equity": 75000,  # Above minimum
            "property_value": 250000,  # In range
            "motivation": "foreclosure, need to sell fast"  # Good motivation
        })

        with patch('agents.workflows.enhanced_lead_processor.AgentBase.__init__', return_value=None):
            processor = EnhancedLeadProcessor(lead_id="test_lead_123")
            processor.context = {"lead": qualified_data}
            processor.lead_data = qualified_data

            qualification = processor.qualify_lead()

            self.assertTrue(qualification.qualified)
            self.assertGreater(qualification.qualification_score, 70)

    def test_disqualified_lead(self):
        """Test qualification for a poor lead"""
        disqualified_data = self.sample_lead_data.copy()
        disqualified_data.update({
            "equity": 10000,  # Below minimum
            "motivation": "not motivated, just curious"  # Poor motivation
        })

        with patch('agents.workflows.enhanced_lead_processor.AgentBase.__init__', return_value=None):
            processor = EnhancedLeadProcessor(lead_id="test_lead_123")
            processor.context = {"lead": disqualified_data}
            processor.lead_data = disqualified_data

            qualification = processor.qualify_lead()

            self.assertFalse(qualification.qualified)
            self.assertGreater(len(qualification.disqualification_reasons), 0)

class TestIntelligentFollowUp(unittest.TestCase):
    """Test cases for intelligent follow-up system"""

    def setUp(self):
        """Set up test fixtures"""
        self.sample_lead_data = {
            "lead_id": "test_lead_123",
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "phone": "+1234567890",
            "property_address": "123 Main St",
            "ghl_contact_id": "ghl_123"
        }

    def test_follow_up_sequence_creation(self):
        """Test creation of personalized follow-up sequences"""
        with patch('agents.workflows.intelligent_followup.AgentBase.__init__', return_value=None):
            followup = IntelligentFollowUp(lead_id="test_lead_123")
            followup.context = {"lead": self.sample_lead_data}
            followup.lead_data = self.sample_lead_data

            # Test Tier 1 high priority sequence
            sequence = followup.create_personalized_sequence("high", 1)

            self.assertGreater(len(sequence.actions), 0)
            self.assertIsInstance(sequence.total_duration_days, int)
            self.assertIsInstance(sequence.success_criteria, dict)

    def test_message_personalization(self):
        """Test message personalization"""
        with patch('agents.workflows.intelligent_followup.AgentBase.__init__', return_value=None):
            followup = IntelligentFollowUp(lead_id="test_lead_123")
            followup.context = {"lead": self.sample_lead_data}
            followup.lead_data = self.sample_lead_data

            template = "Hi {first_name}! Thanks for your interest in selling {property_address}."
            personalization_data = followup._get_personalization_data()

            personalized = followup.personalize_message(template, personalization_data)

            self.assertIn("John", personalized)
            self.assertIn("123 Main St", personalized)
            self.assertNotIn("{first_name}", personalized)

    def test_optimal_timing_calculation(self):
        """Test optimal timing calculation for follow-ups"""
        with patch('agents.workflows.intelligent_followup.AgentBase.__init__', return_value=None):
            followup = IntelligentFollowUp(lead_id="test_lead_123")
            followup.context = {"lead": self.sample_lead_data}
            followup.lead_data = self.sample_lead_data

            from agents.workflows.intelligent_followup import FollowUpAction
            action = FollowUpAction(
                channel=FollowUpChannel.SMS,
                stage=FollowUpStage.INITIAL_CONTACT,
                delay_hours=2,
                message_template="test",
                personalization_data={},
                priority="high",
                expected_response_rate=0.3
            )

            base_time = datetime(2024, 1, 1, 8, 0, 0)  # Monday 8 AM
            optimal_time = followup.calculate_optimal_timing(action, base_time)

            # Should be adjusted to business hours
            self.assertGreaterEqual(optimal_time.hour, 9)
            self.assertLessEqual(optimal_time.hour, 18)

class TestAPIEndpoints(unittest.TestCase):
    """Test cases for API endpoints"""

    def setUp(self):
        """Set up test fixtures"""
        self.api_base_url = "http://localhost:5002/api/v1"
        self.test_lead_data = {
            "lead_id": "test_api_lead_123",
            "lead_data": {
                "first_name": "Jane",
                "last_name": "Smith",
                "email": "<EMAIL>",
                "property_value": 200000,
                "equity": 60000
            }
        }

    def test_health_endpoint(self):
        """Test API health endpoint"""
        try:
            response = requests.get(f"{self.api_base_url}/health", timeout=5)
            self.assertEqual(response.status_code, 200)

            data = response.json()
            self.assertEqual(data["status"], "healthy")
            self.assertIn("service", data)
        except requests.exceptions.RequestException:
            self.skipTest("API server not running")

    def test_enhanced_processing_endpoint(self):
        """Test enhanced lead processing endpoint"""
        try:
            response = requests.post(
                f"{self.api_base_url}/leads/process-enhanced",
                json=self.test_lead_data,
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                self.assertTrue(data.get("success"))
                self.assertIn("lead_score", data)
                self.assertIn("qualification", data)
        except requests.exceptions.RequestException:
            self.skipTest("API server not running")

class TestEndToEndProcessing(unittest.TestCase):
    """End-to-end integration tests"""

    def test_complete_lead_processing_workflow(self):
        """Test complete lead processing from GHL webhook to follow-up"""
        # Mock GHL webhook data
        ghl_webhook_data = {
            "contact": {
                "id": "ghl_test_123",
                "firstName": "Test",
                "lastName": "User",
                "email": "<EMAIL>",
                "phone": "+1234567890",
                "customFields": [
                    {"name": "property_value", "value": "250000"},
                    {"name": "equity", "value": "75000"},
                    {"name": "motivation", "value": "foreclosure"}
                ]
            }
        }

        # This would test the complete workflow:
        # 1. GHL webhook processing
        # 2. Lead scoring and qualification
        # 3. Follow-up sequence creation
        # 4. Workflow triggering

        # For now, we'll just verify the data structure
        self.assertIn("contact", ghl_webhook_data)
        self.assertIn("customFields", ghl_webhook_data["contact"])

def run_tests():
    """Run all test suites"""
    # Create test suite
    test_suite = unittest.TestSuite()

    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestEnhancedLeadProcessor))
    test_suite.addTest(unittest.makeSuite(TestIntelligentFollowUp))
    test_suite.addTest(unittest.makeSuite(TestAPIEndpoints))
    test_suite.addTest(unittest.makeSuite(TestEndToEndProcessing))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # Return success status
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
