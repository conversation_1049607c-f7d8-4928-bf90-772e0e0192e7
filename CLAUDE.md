# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## System Overview

AI-OS is an **AI-powered real estate lead processing and investment analysis system** that automates the entire workflow from lead ingestion to property analysis, offer generation, and disposition management. The system uses multiple AI agents, external APIs, and n8n workflow automation to streamline real estate investment operations.

## Architecture

### Multi-Service Docker Stack
- **PostgreSQL** (port 5433) - n8n workflow storage  
- **n8n** (port 5678) - Workflow automation engine
- **Flask API** (port 5002) - Core Python application
- **Knowledge Pipeline** - Document ingestion and vector search
- **Optional Retool** (port 3000) - Business intelligence dashboard

### Core Technology Stack
- **Python 3.12** with Flask/FastAPI for AI agents and API services
- **Node.js** for webhook handlers and real-time services
- **PostgreSQL + pgvector** for data storage and vector search
- **Supabase** as primary database with real-time features
- **n8n** for workflow orchestration
- **Multi-LLM routing** (OpenAI, Anthropic, Mistral, Together.ai)

## Development Commands

### Docker Operations
```bash
# Start all services
docker compose up -d

# View logs
docker compose logs -f api
docker compose logs -f n8n

# Stop services
docker compose down

# Rebuild API with no cache
docker compose build --no-cache api
```

### Python Development
```bash
# Setup environment (run once)
./scripts/setup_env.sh

# Activate virtual environment
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run API server locally
python knowledge_pipeline/ingestion_api.py

# Run system orchestrator
python scripts/run_system.py
```

### Node.js Services
```bash
# Install dependencies
npm install

# Start webhook server
npm start

# Development mode
npm run dev

# Register GHL webhooks
npm run register-webhook
```

### Testing
```bash
# Run all tests
./scripts/run_all_tests.sh

# Run specific test suites
python tests/test_integration.py
python tests/test_offer_generation.py
python tests/test_follow_up_scheduler.py
python tests/test_mao_workflow.py
python tests/test_memory_threading.py

# Health check
python scripts/test_health_endpoint.py

# End-to-end test
python scripts/run_end_to_end_test.py
```

## Key Components

### AI Agent System
All agents inherit from `agents/agent_base.py` and include:
- **Lead Qualification Agent** - Processes and enriches incoming leads
- **Comping Agent** - Orchestrates property comparables gathering
- **MAO Calculator** - Calculates Maximum Allowable Offer
- **Offer Generator** - Creates AI-powered property offers
- **Follow-Up Bot** - Automated lead nurturing
- **Disposition Bot** - Property disposition strategies
- **Exit Strategy Bot** - Investment strategy analysis

Agent features:
- Contextual memory management in `memory_store/`
- Intelligent model routing via `agents/model_router.py`
- Supabase integration for persistence
- Configurable workflows in `config/workflows.json`

### Data Flow
```
GHL Lead Sources → n8n Webhooks → Flask API → AI Agents → Supabase → Retool Dashboard
```

### Configuration Files
- `config/workflows.json` - Business rules and process definitions
- `config/models.json` - AI model routing and cost optimization  
- `config/ghl_config.json` - CRM integration settings
- `.env` - Environment variables and API keys

### Web Scrapers
Multiple scrapers in `scrapers/` directory:
- `batchleads_scraper.py` - Property data from BatchLeads
- `privy_scraper.py` - Privy property data
- `lotside_scraper.py` - Lotside comparables
- `browser_automation.py` - Shared automation utilities

### Knowledge Pipeline
Document ingestion system in `knowledge_pipeline/`:
- Upload and process documents via `/api/v1/documents/ingest-file`
- Vector storage with pgvector for similarity search
- Template management for follow-up messages
- Tag-based document organization

### Memory Management
Sophisticated memory system in `memory_store/`:
- Agent-specific contextual memory
- Thread-safe operations for concurrent agents
- Memory compression for long-running processes
- Audit logging in `summary_audit_log.jsonl`

## Database Schema

Key Supabase tables:
- `leads` - Contact and property lead information
- `properties` - Property details and analysis data
- `comps` - Property comparables from multiple sources  
- `offers` - Generated offers and tracking
- `kb_documents/kb_chunks` - Knowledge base with vector embeddings
- `workflows` - Process automation tracking

**Important:** Run `supabase db push` after pulling KB schema migrations to avoid 500 errors.

## n8n Workflows

Three main workflows in `n8n/workflows/`:
- `ghl_inbound_lead.json` - Processes incoming GHL leads
- `aios_outbound_update_ghl.json` - Updates GHL with offer data
- `util_ghl_alert.json` - System notifications

Configure HTTP Basic Auth credential named "GHL Location Key" with GHL API key as password.

## Environment Setup

Essential environment variables:
```bash
# Database
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
DATABASE_URL=postgres://...

# APIs
GHL_API_KEY=your_ghl_api_key
GHL_LOCATION_ID=your_location_id
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key

# Services  
N8N_API_KEY=your_n8n_key
PORT=5002
WEBHOOK_SECRET=your_secret
```

Copy `.env.example` to `.env` and populate with actual credentials.

## Common Development Patterns

### Running Individual Components
```bash
# Knowledge pipeline API
python knowledge_pipeline/ingestion_api.py

# Follow-up scheduler  
python scripts/run_follow_up_scheduler.py

# Offer generation
python scripts/run_offer_generation.py

# MAO calculator test
python test_mao_calculator.py
```

### Agent Development
- Inherit from `agents/agent_base.py`
- Use `agents/llm_client.py` for model interactions
- Implement context management via `agents/agent_context.py`
- Follow existing patterns in agent implementations

### Error Handling
- Comprehensive logging in `logs/` directory
- Health check endpoints for service monitoring
- Structured error logging to Supabase
- Memory audit logging for debugging

### Retool Dashboard
Import dashboard from `retool/lead_manager_export.json`:
- Configure PostgreSQL resource for Supabase
- Set up REST API resource for n8n webhooks
- Use environment variables for credentials