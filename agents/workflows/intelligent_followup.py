#!/usr/bin/env python3
"""
Intelligent Follow-up Automation System

This module provides intelligent follow-up automation including:
- Personalized message generation based on lead data
- Optimal timing calculation based on lead behavior
- Multi-channel communication (SMS, Email, Phone)
- Response tracking and adaptation
- A/B testing for message optimization
"""

import os
import logging
import json
import random
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from agents.agent_base import AgentBase
from agents.followup_bot import FollowUpBot
from agents.ghl_client import GHLClient
from knowledge_pipeline.utils.query_kb import query_kb

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FollowUpChannel(Enum):
    """Communication channels for follow-up"""
    SMS = "sms"
    EMAIL = "email"
    PHONE = "phone"
    VOICEMAIL = "voicemail"

class FollowUpStage(Enum):
    """Follow-up stages"""
    INITIAL_CONTACT = "initial_contact"
    INTEREST_CONFIRMATION = "interest_confirmation"
    INFORMATION_GATHERING = "information_gathering"
    OFFER_PRESENTATION = "offer_presentation"
    NEGOTIATION = "negotiation"
    CLOSING = "closing"
    NURTURE = "nurture"

class ResponseType(Enum):
    """Types of responses from leads"""
    POSITIVE = "positive"
    NEUTRAL = "neutral"
    NEGATIVE = "negative"
    NO_RESPONSE = "no_response"
    OPT_OUT = "opt_out"

@dataclass
class FollowUpAction:
    """Individual follow-up action"""
    channel: FollowUpChannel
    stage: FollowUpStage
    delay_hours: float
    message_template: str
    personalization_data: Dict[str, Any]
    priority: str
    expected_response_rate: float

@dataclass
class FollowUpSequence:
    """Complete follow-up sequence"""
    lead_id: str
    sequence_id: str
    actions: List[FollowUpAction]
    total_duration_days: int
    success_criteria: Dict[str, Any]
    fallback_actions: List[FollowUpAction]

class IntelligentFollowUp(AgentBase):
    """
    Intelligent follow-up system with personalization and optimization
    """

    def __init__(self, lead_id: str, entity: str = "lead"):
        super().__init__(agent_name="intelligent_followup", lead_id=lead_id, entity=entity)
        self.lead_data = getattr(self, 'context', {}).get("lead", {})
        self.ghl_client = GHLClient()
        self.message_templates = self._load_message_templates()
        self.timing_rules = self._load_timing_rules()

    def _load_message_templates(self) -> Dict[str, Dict[str, List[str]]]:
        """Load message templates from knowledge base"""
        try:
            templates_context = query_kb("What are the follow-up message templates for different stages and channels?")
            # Parse templates or use defaults
            return {
                "initial_contact": {
                    "sms": [
                        "Hi {first_name}! Thanks for your interest in selling {property_address}. I'd love to discuss your situation. When's a good time to chat?",
                        "Hello {first_name}, I received your information about {property_address}. I can help you sell quickly. Are you available for a brief call?",
                        "Hi there! I'm {agent_name} and I help homeowners sell fast. I'd like to learn more about {property_address}. Can we talk today?"
                    ],
                    "email": [
                        "Subject: Quick Cash Offer for {property_address}\n\nHi {first_name},\n\nThank you for reaching out about selling {property_address}. I specialize in helping homeowners sell quickly for cash.\n\nI'd love to learn more about your situation and see how I can help. Are you available for a quick 10-minute call today?\n\nBest regards,\n{agent_name}",
                        "Subject: Your Property Inquiry - {property_address}\n\nHello {first_name},\n\nI hope this email finds you well. I received your inquiry about {property_address} and I'm excited to help.\n\nI can typically close in 7-14 days with cash. Would you like to discuss your timeline and goals?\n\nLooking forward to hearing from you,\n{agent_name}"
                    ]
                },
                "interest_confirmation": {
                    "sms": [
                        "Hi {first_name}, following up on {property_address}. Are you still looking to sell? I have some great options for you.",
                        "Hello! Just checking in about {property_address}. I'd love to help you move forward. Still interested in selling?",
                        "Hi {first_name}, wanted to touch base about your property. Are you ready to move forward with selling {property_address}?"
                    ],
                    "email": [
                        "Subject: Still interested in selling {property_address}?\n\nHi {first_name},\n\nI wanted to follow up on our previous conversation about {property_address}. Are you still looking to sell?\n\nI have several options that might work for your situation. Would you like to schedule a quick call to discuss?\n\nBest,\n{agent_name}"
                    ]
                },
                "offer_presentation": {
                    "sms": [
                        "Hi {first_name}! I've completed my analysis of {property_address}. I can offer ${offer_amount:,.0f}. Interested in discussing?",
                        "Hello! Great news - I can make an offer of ${offer_amount:,.0f} for {property_address}. Can we talk about the details?",
                        "Hi {first_name}, I'm ready to present my offer for {property_address}: ${offer_amount:,.0f}. When can we discuss?"
                    ],
                    "email": [
                        "Subject: Cash Offer Ready - ${offer_amount:,.0f} for {property_address}\n\nHi {first_name},\n\nI've completed my analysis and I'm ready to make you a cash offer for {property_address}.\n\nOffer Amount: ${offer_amount:,.0f}\nClosing Timeline: {closing_days} days\nNo repairs needed\nNo realtor commissions\n\nWould you like to discuss the details? I'm available for a call today.\n\nBest regards,\n{agent_name}"
                    ]
                }
            }
        except Exception as e:
            logger.warning(f"Could not load message templates: {e}, using defaults")
            return self._get_default_templates()

    def _get_default_templates(self) -> Dict[str, Dict[str, List[str]]]:
        """Get default message templates"""
        return {
            "initial_contact": {
                "sms": ["Hi {first_name}! Thanks for your interest in selling. Can we chat about {property_address}?"],
                "email": ["Subject: Your Property Inquiry\n\nHi {first_name},\n\nThanks for your interest. Let's discuss {property_address}.\n\nBest,\n{agent_name}"]
            }
        }

    def _load_timing_rules(self) -> Dict[str, Any]:
        """Load timing rules for follow-ups"""
        return {
            "business_hours": {"start": 9, "end": 18},
            "time_zone": "America/New_York",
            "weekdays_only": True,
            "optimal_times": {
                "sms": [10, 14, 16],  # 10am, 2pm, 4pm
                "email": [9, 13, 17],  # 9am, 1pm, 5pm
                "phone": [10, 14, 16]  # 10am, 2pm, 4pm
            },
            "delay_multipliers": {
                "high_priority": 0.5,
                "medium_priority": 1.0,
                "low_priority": 2.0
            }
        }

    def create_personalized_sequence(self, lead_priority: str = "medium", lead_tier: int = 2) -> FollowUpSequence:
        """
        Create a personalized follow-up sequence based on lead data

        Args:
            lead_priority: Priority level (high, medium, low)
            lead_tier: Lead tier (1 or 2)

        Returns:
            FollowUpSequence: Personalized follow-up sequence
        """
        actions = []
        sequence_id = f"seq_{self.lead_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"

        # Determine sequence based on tier and priority
        if lead_tier == 1 and lead_priority == "high":
            actions = self._create_tier1_high_priority_sequence()
        elif lead_tier == 1:
            actions = self._create_tier1_standard_sequence()
        else:
            actions = self._create_tier2_nurture_sequence()

        # Calculate total duration
        total_duration = max(action.delay_hours for action in actions) / 24 if actions else 0

        # Define success criteria
        success_criteria = {
            "response_rate_target": 0.3 if lead_tier == 1 else 0.15,
            "conversion_target": 0.1 if lead_tier == 1 else 0.05,
            "engagement_threshold": 2  # Number of positive interactions
        }

        # Create fallback actions for non-responsive leads
        fallback_actions = self._create_fallback_sequence()

        return FollowUpSequence(
            lead_id=self.lead_id,
            sequence_id=sequence_id,
            actions=actions,
            total_duration_days=int(total_duration),
            success_criteria=success_criteria,
            fallback_actions=fallback_actions
        )

    def _create_tier1_high_priority_sequence(self) -> List[FollowUpAction]:
        """Create aggressive sequence for Tier 1 high priority leads"""
        return [
            FollowUpAction(
                channel=FollowUpChannel.PHONE,
                stage=FollowUpStage.INITIAL_CONTACT,
                delay_hours=0.5,  # 30 minutes
                message_template="immediate_phone_call",
                personalization_data=self._get_personalization_data(),
                priority="critical",
                expected_response_rate=0.6
            ),
            FollowUpAction(
                channel=FollowUpChannel.SMS,
                stage=FollowUpStage.INITIAL_CONTACT,
                delay_hours=2,
                message_template="initial_contact_sms_urgent",
                personalization_data=self._get_personalization_data(),
                priority="high",
                expected_response_rate=0.4
            ),
            FollowUpAction(
                channel=FollowUpChannel.EMAIL,
                stage=FollowUpStage.INFORMATION_GATHERING,
                delay_hours=6,
                message_template="detailed_information_request",
                personalization_data=self._get_personalization_data(),
                priority="high",
                expected_response_rate=0.3
            ),
            FollowUpAction(
                channel=FollowUpChannel.SMS,
                stage=FollowUpStage.INTEREST_CONFIRMATION,
                delay_hours=24,
                message_template="interest_confirmation_sms",
                personalization_data=self._get_personalization_data(),
                priority="medium",
                expected_response_rate=0.25
            )
        ]

    def _create_tier1_standard_sequence(self) -> List[FollowUpAction]:
        """Create standard sequence for Tier 1 leads"""
        return [
            FollowUpAction(
                channel=FollowUpChannel.SMS,
                stage=FollowUpStage.INITIAL_CONTACT,
                delay_hours=2,
                message_template="initial_contact_sms",
                personalization_data=self._get_personalization_data(),
                priority="high",
                expected_response_rate=0.35
            ),
            FollowUpAction(
                channel=FollowUpChannel.EMAIL,
                stage=FollowUpStage.INITIAL_CONTACT,
                delay_hours=6,
                message_template="initial_contact_email",
                personalization_data=self._get_personalization_data(),
                priority="medium",
                expected_response_rate=0.25
            ),
            FollowUpAction(
                channel=FollowUpChannel.PHONE,
                stage=FollowUpStage.INFORMATION_GATHERING,
                delay_hours=24,
                message_template="information_gathering_call",
                personalization_data=self._get_personalization_data(),
                priority="medium",
                expected_response_rate=0.4
            ),
            FollowUpAction(
                channel=FollowUpChannel.SMS,
                stage=FollowUpStage.INTEREST_CONFIRMATION,
                delay_hours=72,
                message_template="interest_confirmation_sms",
                personalization_data=self._get_personalization_data(),
                priority="medium",
                expected_response_rate=0.2
            )
        ]

    def _create_tier2_nurture_sequence(self) -> List[FollowUpAction]:
        """Create nurture sequence for Tier 2 leads"""
        return [
            FollowUpAction(
                channel=FollowUpChannel.EMAIL,
                stage=FollowUpStage.INITIAL_CONTACT,
                delay_hours=24,
                message_template="nurture_welcome_email",
                personalization_data=self._get_personalization_data(),
                priority="low",
                expected_response_rate=0.15
            ),
            FollowUpAction(
                channel=FollowUpChannel.SMS,
                stage=FollowUpStage.NURTURE,
                delay_hours=120,  # 5 days
                message_template="nurture_check_in_sms",
                personalization_data=self._get_personalization_data(),
                priority="low",
                expected_response_rate=0.1
            ),
            FollowUpAction(
                channel=FollowUpChannel.EMAIL,
                stage=FollowUpStage.NURTURE,
                delay_hours=240,  # 10 days
                message_template="nurture_market_update",
                personalization_data=self._get_personalization_data(),
                priority="low",
                expected_response_rate=0.08
            )
        ]

    def _create_fallback_sequence(self) -> List[FollowUpAction]:
        """Create fallback sequence for non-responsive leads"""
        return [
            FollowUpAction(
                channel=FollowUpChannel.EMAIL,
                stage=FollowUpStage.NURTURE,
                delay_hours=168,  # 1 week
                message_template="final_attempt_email",
                personalization_data=self._get_personalization_data(),
                priority="low",
                expected_response_rate=0.05
            )
        ]

    def _get_personalization_data(self) -> Dict[str, Any]:
        """Get personalization data for message templates"""
        return {
            "first_name": self.lead_data.get("first_name", "there"),
            "last_name": self.lead_data.get("last_name", ""),
            "property_address": self.lead_data.get("property_address", "your property"),
            "agent_name": "Your Real Estate Investor",
            "company_name": "Advanced Investor OS",
            "phone_number": "******-INVESTOR",
            "email": "<EMAIL>",
            "city": self.lead_data.get("city", ""),
            "state": self.lead_data.get("state", ""),
            "property_value": self.lead_data.get("property_value", 0),
            "equity": self.lead_data.get("equity", 0),
            "motivation": self.lead_data.get("motivation", ""),
            "timeline": self.lead_data.get("timeline", "")
        }

    def personalize_message(self, template: str, personalization_data: Dict[str, Any]) -> str:
        """
        Personalize a message template with lead data

        Args:
            template: Message template with placeholders
            personalization_data: Data for personalization

        Returns:
            Personalized message
        """
        try:
            return template.format(**personalization_data)
        except KeyError as e:
            logger.warning(f"Missing personalization key: {e}")
            # Return template with unfilled placeholders removed
            import re
            return re.sub(r'\{[^}]+\}', '', template)

    def calculate_optimal_timing(self, action: FollowUpAction, base_time: datetime) -> datetime:
        """
        Calculate optimal timing for a follow-up action

        Args:
            action: Follow-up action
            base_time: Base time to calculate from

        Returns:
            Optimal datetime for the action
        """
        # Add the delay
        target_time = base_time + timedelta(hours=action.delay_hours)

        # Adjust for business hours and optimal times
        optimal_hours = self.timing_rules["optimal_times"].get(action.channel.value, [10, 14, 16])

        # Find the next optimal time
        while True:
            # Check if it's a weekday (if required)
            if self.timing_rules["weekdays_only"] and target_time.weekday() >= 5:
                # Move to next Monday
                days_ahead = 7 - target_time.weekday()
                target_time = target_time.replace(hour=optimal_hours[0], minute=0, second=0) + timedelta(days=days_ahead)
                continue

            # Check if it's within business hours
            if target_time.hour < self.timing_rules["business_hours"]["start"]:
                target_time = target_time.replace(hour=optimal_hours[0], minute=0, second=0)
            elif target_time.hour >= self.timing_rules["business_hours"]["end"]:
                # Move to next day
                target_time = (target_time + timedelta(days=1)).replace(hour=optimal_hours[0], minute=0, second=0)
                continue
            else:
                # Find the next optimal hour
                current_hour = target_time.hour
                next_optimal = min([h for h in optimal_hours if h > current_hour], default=None)

                if next_optimal:
                    target_time = target_time.replace(hour=next_optimal, minute=0, second=0)
                else:
                    # Move to next day
                    target_time = (target_time + timedelta(days=1)).replace(hour=optimal_hours[0], minute=0, second=0)
                    continue

            break

        return target_time

    def execute_follow_up_action(self, action: FollowUpAction) -> Dict[str, Any]:
        """
        Execute a single follow-up action

        Args:
            action: Follow-up action to execute

        Returns:
            Execution result
        """
        try:
            # Personalize the message
            personalized_message = self.personalize_message(
                action.message_template,
                action.personalization_data
            )

            # Get GHL contact ID
            ghl_contact_id = self.lead_data.get("ghl_contact_id")
            if not ghl_contact_id:
                return {
                    "success": False,
                    "error": "No GHL contact ID found",
                    "action": action.channel.value
                }

            # Execute based on channel
            if action.channel == FollowUpChannel.SMS:
                result = self.ghl_client.send_sms(ghl_contact_id, personalized_message)
            elif action.channel == FollowUpChannel.EMAIL:
                # Extract subject and body from email template
                if "Subject:" in personalized_message:
                    lines = personalized_message.split('\n', 1)
                    subject = lines[0].replace("Subject:", "").strip()
                    body = lines[1] if len(lines) > 1 else ""
                else:
                    subject = f"Follow-up about {action.personalization_data.get('property_address', 'your property')}"
                    body = personalized_message

                result = self.ghl_client.send_email(ghl_contact_id, subject, body)
            elif action.channel == FollowUpChannel.PHONE:
                # For phone calls, we'll create a task/reminder
                result = self.ghl_client.create_task(
                    ghl_contact_id,
                    f"Call {action.personalization_data.get('first_name', 'lead')} about {action.personalization_data.get('property_address', 'property')}"
                )
            else:
                return {
                    "success": False,
                    "error": f"Unsupported channel: {action.channel.value}",
                    "action": action.channel.value
                }

            # Log the action
            logger.info(f"Executed {action.channel.value} follow-up for lead {self.lead_id}")

            return {
                "success": True,
                "channel": action.channel.value,
                "stage": action.stage.value,
                "message_sent": personalized_message[:100] + "..." if len(personalized_message) > 100 else personalized_message,
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error executing follow-up action: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "action": action.channel.value
            }

    def track_response(self, response_type: ResponseType, response_content: str = "") -> Dict[str, Any]:
        """
        Track lead response and adapt follow-up strategy

        Args:
            response_type: Type of response received
            response_content: Content of the response

        Returns:
            Updated strategy recommendations
        """
        try:
            # Analyze response and update lead scoring
            response_data = {
                "lead_id": self.lead_id,
                "response_type": response_type.value,
                "response_content": response_content,
                "timestamp": datetime.utcnow().isoformat()
            }

            # Determine next actions based on response
            if response_type == ResponseType.POSITIVE:
                next_actions = [
                    "escalate_to_human",
                    "schedule_property_visit",
                    "prepare_offer"
                ]
                follow_up_adjustment = "accelerate"
            elif response_type == ResponseType.NEUTRAL:
                next_actions = [
                    "provide_more_information",
                    "address_concerns",
                    "continue_nurture"
                ]
                follow_up_adjustment = "maintain"
            elif response_type == ResponseType.NEGATIVE:
                next_actions = [
                    "add_to_long_term_nurture",
                    "reduce_frequency",
                    "change_messaging"
                ]
                follow_up_adjustment = "reduce"
            elif response_type == ResponseType.OPT_OUT:
                next_actions = [
                    "add_to_dnc_list",
                    "stop_all_communication"
                ]
                follow_up_adjustment = "stop"
            else:  # NO_RESPONSE
                next_actions = [
                    "try_different_channel",
                    "adjust_timing",
                    "modify_message"
                ]
                follow_up_adjustment = "modify"

            return {
                "response_tracked": True,
                "response_type": response_type.value,
                "next_actions": next_actions,
                "follow_up_adjustment": follow_up_adjustment,
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error tracking response: {str(e)}")
            return {
                "response_tracked": False,
                "error": str(e)
            }

    def run_ab_test(self, message_variants: List[str], test_percentage: float = 0.1) -> Dict[str, Any]:
        """
        Run A/B test on message variants

        Args:
            message_variants: List of message variants to test
            test_percentage: Percentage of leads to include in test

        Returns:
            A/B test setup result
        """
        try:
            # Determine if this lead should be in the test
            import hashlib
            lead_hash = int(hashlib.md5(self.lead_id.encode()).hexdigest(), 16)
            should_test = (lead_hash % 100) < (test_percentage * 100)

            if not should_test:
                return {
                    "ab_test": False,
                    "reason": "Lead not selected for testing"
                }

            # Select random variant
            variant_index = lead_hash % len(message_variants)
            selected_variant = message_variants[variant_index]

            logger.info(f"Lead {self.lead_id} selected for A/B test, variant {variant_index}")

            return {
                "ab_test": True,
                "variant_index": variant_index,
                "selected_message": selected_variant,
                "test_group": f"variant_{variant_index}"
            }

        except Exception as e:
            logger.error(f"Error setting up A/B test: {str(e)}")
            return {
                "ab_test": False,
                "error": str(e)
            }

    def run(self) -> Dict[str, Any]:
        """
        Run the intelligent follow-up system

        Returns:
            Dict containing execution results
        """
        try:
            logger.info(f"Starting intelligent follow-up for lead {self.lead_id}")

            # Get lead priority and tier from context or calculate
            lead_priority = self.lead_data.get("priority", "medium")
            lead_tier = self.lead_data.get("tier", 2)

            # Create personalized sequence
            sequence = self.create_personalized_sequence(lead_priority, lead_tier)
            logger.info(f"Created follow-up sequence with {len(sequence.actions)} actions")

            # Execute immediate actions (delay < 1 hour)
            immediate_actions = [action for action in sequence.actions if action.delay_hours < 1]
            executed_actions = []

            for action in immediate_actions:
                result = self.execute_follow_up_action(action)
                executed_actions.append(result)

            # Schedule remaining actions
            scheduled_actions = []
            base_time = datetime.utcnow()

            for action in sequence.actions:
                if action.delay_hours >= 1:
                    optimal_time = self.calculate_optimal_timing(action, base_time)
                    scheduled_actions.append({
                        "action": action,
                        "scheduled_time": optimal_time.isoformat(),
                        "delay_hours": action.delay_hours
                    })

            return {
                "success": True,
                "lead_id": self.lead_id,
                "sequence_id": sequence.sequence_id,
                "total_actions": len(sequence.actions),
                "immediate_actions_executed": len(executed_actions),
                "actions_scheduled": len(scheduled_actions),
                "executed_results": executed_actions,
                "scheduled_actions": [
                    {
                        "channel": sa["action"].channel.value,
                        "stage": sa["action"].stage.value,
                        "scheduled_time": sa["scheduled_time"],
                        "delay_hours": sa["delay_hours"]
                    } for sa in scheduled_actions
                ],
                "success_criteria": sequence.success_criteria,
                "total_duration_days": sequence.total_duration_days,
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in intelligent follow-up: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "lead_id": self.lead_id,
                "timestamp": datetime.utcnow().isoformat()
            }


# Convenience functions
def create_intelligent_followup(lead_id: str, priority: str = "medium", tier: int = 2) -> Dict[str, Any]:
    """
    Create intelligent follow-up sequence for a lead

    Args:
        lead_id: The ID of the lead
        priority: Lead priority (high, medium, low)
        tier: Lead tier (1 or 2)

    Returns:
        Dict containing follow-up setup results
    """
    followup = IntelligentFollowUp(lead_id=lead_id)
    return followup.run()


def track_lead_response(lead_id: str, response_type: str, response_content: str = "") -> Dict[str, Any]:
    """
    Track a lead's response and adapt strategy

    Args:
        lead_id: The ID of the lead
        response_type: Type of response (positive, neutral, negative, no_response, opt_out)
        response_content: Content of the response

    Returns:
        Dict containing tracking results and recommendations
    """
    followup = IntelligentFollowUp(lead_id=lead_id)
    response_enum = ResponseType(response_type)
    return followup.track_response(response_enum, response_content)
