import os
import logging
from abc import ABC, abstractmethod
from memory_store.log_memory import log_memory
from agents.agent_context import build_agent_context
from dotenv import load_dotenv
from agents.model_router import route_model
from agents.llm_client import call_model
from logs.agent_run_logs import log_agent_run
from typing import Dict, Any, Optional # Added Optional, Dict, Any

# Import the new SupabaseMcpClient
from agents.tools.supabase_mcp_client import SupabaseMcpClient

load_dotenv()


class Agent(ABC):
    
    def __init__(self, agent_name: str, lead_id: str, entity: str):
        self.agent_name = agent_name
        self.lead_id = lead_id
        self.entity = entity
        self.context = build_agent_context(agent=agent_name, lead_id=lead_id, entity=entity)
        self._supabase_mcp_client = None # Lazy load client

    @property
    def supabase_mcp(self):
        if self._supabase_mcp_client is None:
            try:
                self._supabase_mcp_client = SupabaseMcpClient()
            except ValueError as e: # <PERSON>les case where <PERSON><PERSON> is not configured
                logging.error(f"Failed to initialize SupabaseMcpClient for agent {self.agent_name}: {e}")
                # Agent can decide how to handle this (e.g., fallback or raise)
        return self._supabase_mcp_client

    @abstractmethod
    def run(self):
        """
        Implement the agent's core logic.
        """
        pass

    def log(self, memory: str, salience: float = 0.8):
        try:
            log_memory(agent=self.agent_name, entity=self.entity, memory=memory, salience=salience)
        except Exception as e:
            logging.error(f"Failed to log memory: {e}")

    def get_prompt_context(self) -> str:
        kb_blocks = "\n\n".join([chunk["content"] for chunk in self.context["kb_context"]])
        mem_blocks = "\n\n".join([m["memory"] for m in self.context["memory_context"]])
        lead_info = str(self.context["lead"])
        return f"""[LEAD INFO]
{lead_info}

[KNOWLEDGE BASE CONTEXT]
{kb_blocks}

[MEMORY CONTEXT]
{mem_blocks}
"""

    def get_model_config(self, task_type: str, memory_required: bool = True) -> dict:
        return route_model(task_type=task_type, memory_required=memory_required)

    def run_model(self, task_type: str, prompt: str, memory_required: bool = True) -> str:
        result = call_model(
            task_type=task_type,
            prompt=prompt,
            memory_required=memory_required,
            agent_name=self.agent_name
        )
        log_agent_run(self.agent_name, self.lead_id, self.entity, result)
        return result

    def datastore_operation(self, instruction: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Performs a datastore operation using the Supabase MCP.

        Args:
            instruction: Natural language instruction for the datastore.
            params: Additional structured parameters for the operation.

        Returns:
            A dictionary containing the result of the operation.
        """
        if not self.supabase_mcp:
            error_msg = "Supabase MCP client is not available."
            logging.error(f"Agent {self.agent_name}: {error_msg}")
            return {"success": False, "error": error_msg}
        
        try:
            return self.supabase_mcp.execute_instruction(instruction, params)
        except Exception as e:
            logging.error(f"Agent {self.agent_name} encountered an error during datastore operation: {e}")
            return {"success": False, "error": str(e)}


class AgentBase(Agent):
    """
    AgentBase class that inherits from Agent to maintain backward compatibility.
    This class is used by followup_bot.py and other components that expect an AgentBase class.
    """
    
    def __init__(self, agent_name: str, lead_id: str, entity: str):
        super().__init__(agent_name, lead_id, entity)
        
    def run(self):
        """
        Default implementation of the run method.
        Subclasses should override this method with their specific logic.
        
        Returns:
            str: Result of the agent run
        """
        logging.warning(f"Using default run implementation for {self.agent_name}. This should be overridden.")
        return f"Default run implementation for {self.agent_name}"

