-- Migration: Enable extensions
-- Date: 2025-05-28
-- Enable required extensions for the schema

-- Enable UUID extension for primary keys
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable vector extension for embeddings
CREATE EXTENSION IF NOT EXISTS "vector";

-- Migration: Create knowledge base documents table
-- Create kb_documents table for storing document content with vector embeddings

CREATE TABLE IF NOT EXISTS kb_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status TEXT DEFAULT 'active',
    source TEXT,
    category TEXT,
    tags TEXT[]
);

-- Add embedding column separately to avoid potential issues
ALTER TABLE kb_documents ADD COLUMN IF NOT EXISTS embedding VECTOR(1536);

-- Migration: <PERSON><PERSON> leads table
-- <PERSON><PERSON> leads table for storing contact information from GHL

CREATE TABLE IF NOT EXISTS leads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ghl_id TEXT NOT NULL,
    first_name TEXT,
    last_name TEXT,
    email TEXT,
    phone TEXT,
    address JSONB DEFAULT '{}'::jsonb,
    tags TEXT[],
    status TEXT DEFAULT 'new',
    tier INTEGER DEFAULT 3,
    source TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_contacted_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Add unique constraint separately
ALTER TABLE leads ADD CONSTRAINT leads_ghl_id_key UNIQUE (ghl_id);

-- Migration: Create activities table
-- Create activities table for tracking interactions and status changes

CREATE TABLE IF NOT EXISTS activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lead_id UUID NOT NULL,
    activity_type TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb,
    source TEXT,
    agent TEXT,
    workflow_id TEXT
);

-- Add foreign key constraint separately
ALTER TABLE activities ADD CONSTRAINT activities_lead_id_fkey 
    FOREIGN KEY (lead_id) REFERENCES leads(id) ON DELETE CASCADE;

-- Migration: Create offers table
-- Create offers table for storing generated offers and their status

CREATE TABLE IF NOT EXISTS offers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lead_id UUID NOT NULL,
    property_address TEXT NOT NULL,
    offer_amount DECIMAL(12,2) NOT NULL,
    mao_amount DECIMAL(12,2),
    arv DECIMAL(12,2),
    repair_estimate DECIMAL(12,2),
    holding_costs DECIMAL(12,2),
    closing_costs DECIMAL(12,2),
    profit_margin DECIMAL(5,2),
    offer_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expiration_date TIMESTAMP WITH TIME ZONE,
    status TEXT DEFAULT 'draft',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb,
    offer_document_url TEXT
);

-- Add foreign key constraint separately
ALTER TABLE offers ADD CONSTRAINT offers_lead_id_fkey 
    FOREIGN KEY (lead_id) REFERENCES leads(id) ON DELETE CASCADE;

-- Migration: Create system tables
-- Create system_config, agent_errors, and webhook_logs tables

CREATE TABLE IF NOT EXISTS system_config (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by TEXT
);

CREATE TABLE IF NOT EXISTS agent_errors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_name TEXT,
    node_name TEXT,
    error_message TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    input_payload JSONB,
    stack_trace TEXT
);

CREATE TABLE IF NOT EXISTS webhook_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    source TEXT NOT NULL,
    event_type TEXT NOT NULL,
    payload JSONB NOT NULL,
    processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT
);

-- Migration: Create KPI view
-- Create view for KPI summary dashboard

-- Create active tier1 leads count view
CREATE OR REPLACE VIEW vw_active_tier1_leads AS
SELECT COUNT(*) AS count FROM leads WHERE status = 'active' AND tier = 1;

-- Create offers sent today view
CREATE OR REPLACE VIEW vw_offers_sent_today AS
SELECT COUNT(*) AS count FROM offers WHERE offer_date >= CURRENT_DATE;

-- Create deals won MTD view
CREATE OR REPLACE VIEW vw_deals_won_mtd AS
SELECT COUNT(*) AS count FROM offers 
WHERE status = 'accepted' AND offer_date >= DATE_TRUNC('month', CURRENT_DATE);

-- Create OpenAI spend today view
CREATE OR REPLACE VIEW vw_openai_spend_today AS
SELECT COALESCE(SUM(CAST(metadata->>'cost' AS DECIMAL)), 0) AS amount 
FROM activities 
WHERE activity_type = 'openai_call' AND created_at >= CURRENT_DATE;

-- Create combined KPI view
CREATE OR REPLACE VIEW vw_kpi_summary AS
SELECT 
    (SELECT count FROM vw_active_tier1_leads) AS active_tier1_leads,
    (SELECT count FROM vw_offers_sent_today) AS offers_sent_today,
    (SELECT count FROM vw_deals_won_mtd) AS deals_won_mtd,
    (SELECT amount FROM vw_openai_spend_today) AS openai_spend_today;

-- Migration: Create indexes for kb_documents
-- Add indexes for performance optimization on kb_documents table

-- Create status index
CREATE INDEX IF NOT EXISTS idx_kb_documents_status ON kb_documents(status);

-- Create category index
CREATE INDEX IF NOT EXISTS idx_kb_documents_category ON kb_documents(category);

-- Create tags index
CREATE INDEX IF NOT EXISTS idx_kb_documents_tags ON kb_documents USING GIN(tags);

-- Create embedding index separately (most complex)
CREATE INDEX IF NOT EXISTS idx_kb_documents_embedding ON kb_documents USING ivfflat (embedding vector_cosine_ops);

-- Migration: Create indexes for leads
-- Add indexes for performance optimization on leads table

-- Create status index
CREATE INDEX IF NOT EXISTS idx_leads_status ON leads(status);

-- Create tier index
CREATE INDEX IF NOT EXISTS idx_leads_tier ON leads(tier);

-- Create tags index
CREATE INDEX IF NOT EXISTS idx_leads_tags ON leads USING GIN(tags);

-- Create created_at index
CREATE INDEX IF NOT EXISTS idx_leads_created_at ON leads(created_at);

-- Create last_contacted_at index
CREATE INDEX IF NOT EXISTS idx_leads_last_contacted_at ON leads(last_contacted_at);

-- Migration: Create indexes for activities and offers
-- Add indexes for performance optimization on activities and offers tables

-- Create activities lead_id index
CREATE INDEX IF NOT EXISTS idx_activities_lead_id ON activities(lead_id);

-- Create activities activity_type index
CREATE INDEX IF NOT EXISTS idx_activities_activity_type ON activities(activity_type);

-- Create activities created_at index
CREATE INDEX IF NOT EXISTS idx_activities_created_at ON activities(created_at);

-- Create offers lead_id index
CREATE INDEX IF NOT EXISTS idx_offers_lead_id ON offers(lead_id);

-- Create offers status index
CREATE INDEX IF NOT EXISTS idx_offers_status ON offers(status);

-- Create offers offer_date index
CREATE INDEX IF NOT EXISTS idx_offers_offer_date ON offers(offer_date);

-- Create offers expiration_date index
CREATE INDEX IF NOT EXISTS idx_offers_expiration_date ON offers(expiration_date);

-- Migration: Create indexes for system tables
-- Add indexes for performance optimization on system tables

-- Create agent_errors timestamp index
CREATE INDEX IF NOT EXISTS idx_agent_errors_timestamp ON agent_errors(timestamp);

-- Create agent_errors workflow_name index
CREATE INDEX IF NOT EXISTS idx_agent_errors_workflow_name ON agent_errors(workflow_name);

-- Create webhook_logs source index
CREATE INDEX IF NOT EXISTS idx_webhook_logs_source ON webhook_logs(source);

-- Create webhook_logs event_type index
CREATE INDEX IF NOT EXISTS idx_webhook_logs_event_type ON webhook_logs(event_type);

-- Create webhook_logs created_at index
CREATE INDEX IF NOT EXISTS idx_webhook_logs_created_at ON webhook_logs(created_at);

-- Create webhook_logs processed index
CREATE INDEX IF NOT EXISTS idx_webhook_logs_processed ON webhook_logs(processed);

-- Migration: Insert initial system configuration
-- Add default configuration values

-- Insert mao_profit_margin
INSERT INTO system_config (key, value, description)
VALUES ('mao_profit_margin', '0.15', 'Default profit margin for MAO calculations')
ON CONFLICT (key) DO NOTHING;

-- Insert mao_holding_cost_percent
INSERT INTO system_config (key, value, description)
VALUES ('mao_holding_cost_percent', '0.02', 'Default holding cost percentage for MAO calculations')
ON CONFLICT (key) DO NOTHING;

-- Insert mao_closing_cost_percent
INSERT INTO system_config (key, value, description)
VALUES ('mao_closing_cost_percent', '0.03', 'Default closing cost percentage for MAO calculations')
ON CONFLICT (key) DO NOTHING;

-- Insert lead_auto_tier_enabled
INSERT INTO system_config (key, value, description)
VALUES ('lead_auto_tier_enabled', 'true', 'Enable automatic lead tiering')
ON CONFLICT (key) DO NOTHING;

-- Insert webhook_validation_enabled
INSERT INTO system_config (key, value, description)
VALUES ('webhook_validation_enabled', 'true', 'Enable webhook payload validation')
ON CONFLICT (key) DO NOTHING;

-- Insert openai_model
INSERT INTO system_config (key, value, description)
VALUES ('openai_model', 'gpt-4o', 'Default OpenAI model to use for AI tasks')
ON CONFLICT (key) DO NOTHING;

-- Migration: Create updated_at triggers
-- Add triggers to update the updated_at timestamp automatically

-- Create update_updated_at_column function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for kb_documents
CREATE TRIGGER update_kb_documents_updated_at
BEFORE UPDATE ON kb_documents
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for leads
CREATE TRIGGER update_leads_updated_at
BEFORE UPDATE ON leads
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for offers
CREATE TRIGGER update_offers_updated_at
BEFORE UPDATE ON offers
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Migration: Configure Row Level Security
-- Enable RLS and create policies for read-only access

-- Enable RLS on kb_documents
ALTER TABLE kb_documents ENABLE ROW LEVEL SECURITY;

-- Enable RLS on leads
ALTER TABLE leads ENABLE ROW LEVEL SECURITY;

-- Enable RLS on activities
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;

-- Enable RLS on offers
ALTER TABLE offers ENABLE ROW LEVEL SECURITY;

-- Enable RLS on system_config
ALTER TABLE system_config ENABLE ROW LEVEL SECURITY;

-- Enable RLS on agent_errors
ALTER TABLE agent_errors ENABLE ROW LEVEL SECURITY;

-- Enable RLS on webhook_logs
ALTER TABLE webhook_logs ENABLE ROW LEVEL SECURITY;

-- Migration: Create RLS policies
-- Create policies for read-only access to tables

-- Create policy for kb_documents
CREATE POLICY kb_documents_readonly ON kb_documents FOR SELECT USING (true);

-- Create policy for leads
CREATE POLICY leads_readonly ON leads FOR SELECT USING (true);

-- Create policy for activities
CREATE POLICY activities_readonly ON activities FOR SELECT USING (true);

-- Create policy for offers
CREATE POLICY offers_readonly ON offers FOR SELECT USING (true);

-- Create policy for system_config
CREATE POLICY system_config_readonly ON system_config FOR SELECT USING (true);

-- Create policy for agent_errors
CREATE POLICY agent_errors_readonly ON agent_errors FOR SELECT USING (true);

-- Create policy for webhook_logs
CREATE POLICY webhook_logs_readonly ON webhook_logs FOR SELECT USING (true);

-- Migration: Grant permissions
-- Grant permissions to the read-only role

-- Check if role exists before granting permissions
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM pg_roles WHERE rolname = 'ai_readonly'
    ) THEN
        GRANT SELECT ON ALL TABLES IN SCHEMA public TO ai_readonly;
    END IF;
END
$$;
