# Manual Deployment Commands for Contabo VPS (**************)

## Step 1: SSH into your server
```bash
ssh root@**************
# OR if you have a different username:
# ssh your_username@**************
```

## Step 2: Run server setup (on the server)
```bash
# Update system
apt update && apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
systemctl enable docker
systemctl start docker

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Install Git and Caddy
apt install git -y
apt install -y debian-keyring debian-archive-keyring apt-transport-https
curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | tee /etc/apt/sources.list.d/caddy-stable.list
apt update && apt install caddy -y

# Setup firewall
ufw --force enable
ufw allow ssh
ufw allow 80
ufw allow 443
ufw allow 5002
ufw allow 5678

# Create app directory
mkdir -p /opt/AI-OS
cd /opt/AI-OS
```

## Step 3: Get AI-OS code (choose one method)

### Option A: Git clone (if repo is on GitHub)
```bash
git clone YOUR_GITHUB_REPO_URL /opt/AI-OS
cd /opt/AI-OS
```

### Option B: Direct upload from your local machine
```bash
# Run this on your LOCAL machine (not the server)
# Replace with your actual username if not root
rsync -avz --progress -e ssh \
    --exclude 'node_modules' \
    --exclude '.git' \
    --exclude 'venv' \
    --exclude '*.log' \
    --exclude 'logs/' \
    . root@**************:/opt/AI-OS/
```

## Step 4: Setup environment (on the server)
```bash
cd /opt/AI-OS

# Copy your environment file
# You'll need to create this or upload your .env file
cat > .env << 'EOF'
# Copy the contents of your .env file here
# Or upload it separately
EOF
```

## Step 5: Create Caddyfile (on the server)
```bash
cd /opt/AI-OS

cat > Caddyfile << 'EOF'
:80 {
    reverse_proxy /webhook/* localhost:5678
    reverse_proxy /api/* localhost:5002
    reverse_proxy /* localhost:5678
    
    encode gzip
    
    log {
        output file /var/log/caddy/aios.log
        level INFO
    }
}
EOF
```

## Step 6: Deploy services (on the server)
```bash
cd /opt/AI-OS

# Start Docker services
docker-compose pull
docker-compose up -d

# Wait for services
sleep 30

# Start Caddy
mkdir -p /var/log/caddy
caddy stop 2>/dev/null || true
caddy start --config Caddyfile

# Check status
docker-compose ps
curl -f http://localhost:5002/api/v1/health
curl -f http://localhost:5678/healthz
```

## Step 7: Test from outside
```bash
# Test from your local machine
curl -f http://**************/api/v1/health
curl -f http://**************/healthz

# Your webhook URL will be:
# http://**************/webhook/ghl/lead
```

## Quick Commands Summary (run on server):
```bash
# One-liner setup (copy all at once)
apt update && apt upgrade -y && \
curl -fsSL https://get.docker.com | sh && \
systemctl enable docker && systemctl start docker && \
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose && \
chmod +x /usr/local/bin/docker-compose && \
apt install git caddy -y && \
mkdir -p /opt/AI-OS && \
echo "✅ Server setup complete!"
```