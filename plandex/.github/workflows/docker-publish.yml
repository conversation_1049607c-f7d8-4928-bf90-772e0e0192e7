name: Build and publish Docker Image

on:
  release:
    types: [created]
  workflow_dispatch: # enable manual triggering

jobs:
  check_release:
    runs-on: ubuntu-latest
    outputs:
      should_build: ${{ steps.check_release.outputs.should_build }}
      tag: ${{ steps.check_release.outputs.tag }}
    
    steps:
      - name: Check release tag and find latest server tag
        id: check_release
        run: |
          if [ "${{ github.event_name }}" == "release" ]; then
            # This is a release event - check if the tag starts with 'server'
            if [[ "${{ github.ref_name }}" == server* ]]; then
              echo "This is a server release: ${{ github.ref_name }}"
              echo "should_build=true" >> $GITHUB_OUTPUT
              echo "tag=${{ github.ref_name }}" >> $GITHUB_OUTPUT
            else
              echo "This is not a server release. Skipping build."
              echo "should_build=false" >> $GITHUB_OUTPUT
            fi
          else
            # This is a manual workflow_dispatch event
            echo "This is a manual workflow trigger. Proceeding to find latest server tag."
            echo "should_build=true" >> $GITHUB_OUTPUT
            echo "tag=latest_server" >> $GITHUB_OUTPUT
          fi

  build_and_push:
    needs: check_release
    if: needs.check_release.outputs.should_build == 'true'
    runs-on: ubuntu-latest

    steps:
      - name: Check out the repo
        uses: actions/checkout@v2
        with:
          fetch-depth: 0  # Fetch all history and tags

      - name: Find latest server tag
        id: find_tag
        if: needs.check_release.outputs.tag == 'latest_server'
        run: |
          # Find the latest tag that starts with 'server'
          LATEST_SERVER_TAG=$(git tag -l "server*" --sort=-creatordate | head -n 1)
          
          if [ -z "$LATEST_SERVER_TAG" ]; then
            echo "No tags starting with 'server' found."
            echo "skip=true" >> $GITHUB_OUTPUT
          else
            echo "Found latest server tag: $LATEST_SERVER_TAG"
            echo "skip=false" >> $GITHUB_OUTPUT
            echo "tag=$LATEST_SERVER_TAG" >> $GITHUB_OUTPUT
          fi
        shell: bash

      - name: Set release tag
        id: set_tag
        if: needs.check_release.outputs.tag != 'latest_server'
        run: |
          echo "skip=false" >> $GITHUB_OUTPUT
          echo "tag=${{ needs.check_release.outputs.tag }}" >> $GITHUB_OUTPUT

      - name: Skip build if no server tag found
        if: (steps.find_tag.outputs.skip == 'true' && needs.check_release.outputs.tag == 'latest_server')
        run: |
          echo "Skipping build because no tag starting with 'server' was found."
          exit 1

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Log in to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Determine tag to use
        id: determine_tag
        run: |
          if [ "${{ needs.check_release.outputs.tag }}" == "latest_server" ]; then
            TAG="${{ steps.find_tag.outputs.tag }}"
          else
            TAG="${{ needs.check_release.outputs.tag }}"
          fi
          echo "Using tag: $TAG"
          echo "tag=$TAG" >> $GITHUB_OUTPUT

      - name: Sanitize tag name
        id: sanitize
        run: echo "SANITIZED_TAG_NAME=$(echo ${{ steps.determine_tag.outputs.tag }} | tr '/' '-' | tr '+' '-')" >> $GITHUB_OUTPUT

      - name: Build and push
        uses: docker/build-push-action@v2
        with:
          context: ./app/
          file: ./app/server/Dockerfile
          push: true
          platforms: linux/amd64,linux/arm64
          tags: |
            plandexai/plandex-server:${{ steps.sanitize.outputs.SANITIZED_TAG_NAME }}
            plandexai/plandex-server:latest