package shared

func IsEmailServiceDomain(domain string) bool {
	_, ok := emailServiceDomains[domain]
	return ok
}

var emailServiceDomains = map[string]bool{
	"gmail.com":        true,
	"yahoo.com":        true,
	"outlook.com":      true,
	"hotmail.com":      true,
	"icloud.com":       true,
	"zoho.com":         true,
	"protonmail.com":   true,
	"mail.com":         true,
	"yandex.com":       true,
	"fastmail.com":     true,
	"tutanota.com":     true,
	"hey.com":          true,
	"hushmail.com":     true,
	"runbox.com":       true,
	"mailfence.com":    true,
	"disroot.org":      true,
	"posteo.de":        true,
	"mailinator.com":   true,
	"aol.com":          true,
	"hotmail.co.uk":    true,
	"hotmail.fr":       true,
	"msn.com":          true,
	"yahoo.fr":         true,
	"wanadoo.fr":       true,
	"orange.fr":        true,
	"comcast.net":      true,
	"yahoo.co.uk":      true,
	"yahoo.com.br":     true,
	"yahoo.co.in":      true,
	"live.com":         true,
	"rediffmail.com":   true,
	"free.fr":          true,
	"gmx.de":           true,
	"web.de":           true,
	"yandex.ru":        true,
	"ymail.com":        true,
	"libero.it":        true,
	"uol.com.br":       true,
	"bol.com.br":       true,
	"mail.ru":          true,
	"cox.net":          true,
	"hotmail.it":       true,
	"sbcglobal.net":    true,
	"sfr.fr":           true,
	"live.fr":          true,
	"verizon.net":      true,
	"live.co.uk":       true,
	"googlemail.com":   true,
	"yahoo.es":         true,
	"ig.com.br":        true,
	"live.nl":          true,
	"bigpond.com":      true,
	"terra.com.br":     true,
	"yahoo.it":         true,
	"neuf.fr":          true,
	"yahoo.de":         true,
	"alice.it":         true,
	"rocketmail.com":   true,
	"att.net":          true,
	"laposte.net":      true,
	"facebook.com":     true,
	"bellsouth.net":    true,
	"yahoo.in":         true,
	"hotmail.es":       true,
	"charter.net":      true,
	"yahoo.ca":         true,
	"yahoo.com.au":     true,
	"rambler.ru":       true,
	"hotmail.de":       true,
	"tiscali.it":       true,
	"shaw.ca":          true,
	"yahoo.co.jp":      true,
	"sky.com":          true,
	"earthlink.net":    true,
	"optonline.net":    true,
	"freenet.de":       true,
	"t-online.de":      true,
	"aliceadsl.fr":     true,
	"virgilio.it":      true,
	"home.nl":          true,
	"qq.com":           true,
	"telenet.be":       true,
	"me.com":           true,
	"yahoo.com.ar":     true,
	"tiscali.co.uk":    true,
	"yahoo.com.mx":     true,
	"voila.fr":         true,
	"gmx.net":          true,
	"planet.nl":        true,
	"tin.it":           true,
	"live.it":          true,
	"ntlworld.com":     true,
	"arcor.de":         true,
	"yahoo.co.id":      true,
	"frontiernet.net":  true,
	"hetnet.nl":        true,
	"live.com.au":      true,
	"yahoo.com.sg":     true,
	"zonnet.nl":        true,
	"club-internet.fr": true,
	"juno.com":         true,
	"optusnet.com.au":  true,
	"blueyonder.co.uk": true,
	"bluewin.ch":       true,
	"skynet.be":        true,
	"sympatico.ca":     true,
	"windstream.net":   true,
	"mac.com":          true,
	"centurytel.net":   true,
	"chello.nl":        true,
	"live.ca":          true,
	"aim.com":          true,
	"bigpond.net.au":   true,
	"163.com":          true,
	"126.com":          true,
	"daum.net":         true,
	"naver.com":        true,
	"sina.com":         true,
	"sohu.com":         true,
	"gmx.com":          true,
	"yopmail.com":      true,
	"bk.ru":            true,
	"list.ru":          true,
	"seznam.cz":        true,
	"nextmail.ru":      true,
	"kpnmail.nl":       true,
}
