module plandex-cli

go 1.23.3

require (
	github.com/charmbracelet/lipgloss v1.0.0
	github.com/chromedp/cdproto v0.0.0-20250319231242-a755498943c8
	github.com/chromedp/chromedp v0.13.3
	github.com/coreos/go-systemd/v22 v22.5.0
	github.com/davecgh/go-spew v1.1.1
	github.com/fatih/color v1.18.0
	github.com/godbus/dbus/v5 v5.1.0
	github.com/google/uuid v1.6.0
	github.com/lithammer/fuzzysearch v1.1.8
	github.com/muesli/reflow v0.3.0
	github.com/muesli/termenv v0.15.3-0.20240618155329-98d742f6907a
	github.com/olekukonko/tablewriter v0.0.5
	github.com/plandex-ai/go-prompt v0.0.0-20250304173555-1f364907fc6c
	github.com/plandex-ai/survey/v2 v2.3.7
	github.com/sashabaranov/go-openai v1.38.1
	github.com/shopspring/decimal v1.4.0
	github.com/spf13/cobra v1.8.0
	golang.org/x/term v0.19.0
	gopkg.in/natefinch/lumberjack.v2 v2.2.1
)

require (
	github.com/alecthomas/chroma v0.10.0 // indirect
	github.com/andybalholm/cascadia v1.3.2 // indirect
	github.com/atotto/clipboard v0.1.4 // indirect
	github.com/aymanbagabas/go-osc52/v2 v2.0.1 // indirect
	github.com/aymerick/douceur v0.2.0 // indirect
	github.com/charmbracelet/x/ansi v0.8.0 // indirect
	github.com/charmbracelet/x/term v0.2.1 // indirect
	github.com/chromedp/sysutil v1.1.0 // indirect
	github.com/cqroot/multichoose v0.1.1 // indirect
	github.com/dlclark/regexp2 v1.11.5 // indirect
	github.com/erikgeiser/coninput v0.0.0-20211004153227-1c3628e74d0f // indirect
	github.com/go-json-experiment/json v0.0.0-20250211171154-1ae217ad3535 // indirect
	github.com/gobwas/httphead v0.1.0 // indirect
	github.com/gobwas/pool v0.2.1 // indirect
	github.com/gobwas/ws v1.4.0 // indirect
	github.com/gorilla/css v1.0.1 // indirect
	github.com/kballard/go-shellquote v0.0.0-20180428030007-95032a82bc51 // indirect
	github.com/lucasb-eyer/go-colorful v1.2.0 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-localereader v0.0.1 // indirect
	github.com/mattn/go-tty v0.0.3 // indirect
	github.com/mgutz/ansi v0.0.0-20170206155736-9520e82c474b // indirect
	github.com/microcosm-cc/bluemonday v1.0.26 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/muesli/ansi v0.0.0-20230316100256-276c6243b2f6 // indirect
	github.com/muesli/cancelreader v0.2.2 // indirect
	github.com/pkg/term v1.2.0-beta.2 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/yuin/goldmark v1.6.0 // indirect
	github.com/yuin/goldmark-emoji v1.0.2 // indirect
	golang.org/x/exp v0.0.0-20241108190413-2d47ceb2692f // indirect
	golang.org/x/net v0.18.0 // indirect
	golang.org/x/sync v0.12.0 // indirect
	golang.org/x/sys v0.30.0 // indirect
)

require (
	github.com/Masterminds/semver v1.5.0
	github.com/PuerkitoBio/goquery v1.8.1
	github.com/briandowns/spinner v1.23.0
	github.com/charmbracelet/bubbles v0.20.0
	github.com/charmbracelet/bubbletea v1.3.0
	github.com/charmbracelet/glamour v0.6.0
	github.com/charmbracelet/glow v1.5.1
	github.com/cqroot/prompt v0.9.3
	github.com/eiannone/keyboard v0.0.0-20220611211555-0d226195f203
	github.com/inconshreveable/go-update v0.0.0-20160112193335-8152e7eb6ccf
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/mattn/go-runewidth v0.0.16 // indirect
	github.com/pkg/browser v0.0.0-20240102092130-5ac0b6a4141c
	github.com/pkoukk/tiktoken-go v0.1.7 // indirect
	github.com/sabhiram/go-gitignore v0.0.0-20210923224102-525f6e181f06
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/xlab/treeprint v1.2.0
	golang.org/x/image v0.25.0 // indirect
	golang.org/x/text v0.23.0 // indirect
	plandex-shared v0.0.0-00010101000000-000000000000
)

replace plandex-shared => ../shared
