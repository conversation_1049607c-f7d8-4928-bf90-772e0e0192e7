I'll work on the current subtask "Update commit message prompts and handling". This involves updating both the prompt in describe.go and the handling in commit_msg.go to use XML instead of JSON.

First, let's update the prompt in describe.go:

**Updating `server/model/prompts/describe.go`:** I'll replace the function definition and update the system prompt to use XML output instead of JSON.

<PlandexBlock lang="go" path="server/model/prompts/describe.go">
package prompts

import (
	"fmt"

	"plandex-shared"
)

const SysDescribe = `You are an AI parser. You turn an AI's plan for a programming task into a structured description. You must output a commit message for the changes proposed using XML tags.

Output your response using a <commitMsg> tag. Do not use any XML attributes, just the tag content.

Example:
<commitMsg>Add user authentication system with JWT support</commitMsg>

You must ALWAYS output a commit message using the XML format shown above. The commit message should be good and succinct.`

var SysDescribeNumTokens int

const SysPendingResults = "You are an AI commit message summarizer. You take a list of descriptions of pending changes and turn them into a succinct one-line summary of all the pending changes that makes for a good commit message title. Output ONLY this one-line title and nothing else."

var SysPendingResultsNumTokens int

func init() {
	var err error
	SysDescribeNumTokens, err = shared.GetNumTokensEstimate(SysDescribe)

	if err != nil {
		panic(fmt.Sprintf("Error getting num tokens for describe plan prompt: %v\n", err))
	}

	SysPendingResultsNumTokens, err = shared.GetNumTokensEstimate(SysPendingResults)

	if err != nil {
		panic(fmt.Sprintf("Error getting num tokens for pending results prompt: %v\n", err))
	}
}
</PlandexBlock>

Now let's update the commit message handling in commit_msg.go:

**Updating `server/model/plan/commit_msg.go`:** I'll update the genPlanDescription method to handle XML output instead of JSON.

<PlandexBlock lang="go" path="server/model/plan/commit_msg.go">
// ... existing code ...

func (state *activeTellStreamState) genPlanDescription() (*db.ConvoMessageDescription, error) {
	auth := state.auth
	plan := state.plan
	planId := plan.Id
	branch := state.branch
	settings := state.settings
	clients := state.clients
	config := settings.ModelPack.CommitMsg
	envVar := config.BaseModelConfig.ApiKeyEnvVar
	client := clients[envVar]

	activePlan := GetActivePlan(planId, branch)
	if activePlan == nil {
		return nil, fmt.Errorf("active plan not found")
	}

	numTokens := prompts.ExtraTokensPerRequest + (prompts.ExtraTokensPerMessage * 2) + prompts.SysDescribeNumTokens + activePlan.NumTokens

	_, apiErr := hooks.ExecHook(hooks.WillSendModelRequest, hooks.HookParams{
		Auth: auth,
		Plan: plan,
		WillSendModelRequestParams: &hooks.WillSendModelRequestParams{
			InputTokens:  numTokens,
			OutputTokens: config.BaseModelConfig.MaxOutputTokens,
			ModelName:    config.BaseModelConfig.ModelName,
		},
	})
	if apiErr != nil {
		return nil, errors.New(apiErr.Msg)
	}

	log.Println("Sending plan description model request")

	descResp, err := model.CreateChatCompletionWithRetries(
		client,
		activePlan.Ctx,
		openai.ChatCompletionRequest{
			Model: config.BaseModelConfig.ModelName,
			Messages: []openai.ChatCompletionMessage{
				{
					Role:    openai.ChatMessageRoleSystem,
					Content: prompts.SysDescribe,
				},
				{
					Role:    openai.ChatMessageRoleAssistant,
					Content: activePlan.CurrentReplyContent,
				},
			},
			Temperature: config.Temperature,
			TopP:        config.TopP,
		},
	)

	if err != nil {
		fmt.Printf("Error during plan description model call: %v\n", err)
		return nil, err
	}

	log.Println("Plan description model call complete")

	if len(descResp.Choices) == 0 {
		return nil, fmt.Errorf("No choices in model response")
	}

	content := descResp.Choices[0].Message.Content
	commitMsg := GetXMLContent(content, "commitMsg")

	if commitMsg == "" {
		return nil, fmt.Errorf("No commit message found in response")
	}

	var inputTokens int
	var outputTokens int
	if descResp.Usage.CompletionTokens > 0 {
		inputTokens = descResp.Usage.PromptTokens
		outputTokens = descResp.Usage.CompletionTokens
	} else {
		inputTokens = numTokens
		outputTokens, err = shared.GetNumTokensEstimate(content)

		if err != nil {
			return nil, fmt.Errorf("error getting num tokens for content: %v", err)
		}
	}

	log.Println("Sending DidSendModelRequest hook")

	_, apiErr = hooks.ExecHook(hooks.DidSendModelRequest, hooks.HookParams{
		Auth: auth,
		Plan: plan,
		DidSendModelRequestParams: &hooks.DidSendModelRequestParams{
			InputTokens:   inputTokens,
			OutputTokens:  outputTokens,
			ModelName:     config.BaseModelConfig.ModelName,
			ModelProvider: config.BaseModelConfig.Provider,
			ModelPackName: settings.ModelPack.Name,
			ModelRole:     shared.ModelRoleCommitMsg,
			Purpose:       "Generated commit message for suggested changes",
		},
	})

	if apiErr != nil {
		return nil, errors.New(apiErr.Msg)
	}

	log.Println("DidSendModelRequest hook complete")

	return &db.ConvoMessageDescription{
		PlanId:    planId,
		CommitMsg: commitMsg,
	}, nil
}

// ... existing code ...
</PlandexBlock>

**Update commit message prompts and handling** has been completed.