Given that a summarization utility exists, the plan can be further simplified to utilize that. Here is the updated plan:

1. Update the shared/types.go file to introduce a new boolean field indicating if a message has been summarized.

   - shared/types.go:

   <PlandexBlock lang="go">
   type ChatCompletionMessage struct {
   	// existing fields
   	...
   	Summarized bool
   }
   </PlandexBlock>

2. Modify the loadConversation function in cli/lib/conversation.go to take advantage of this new field.

   - cli/lib/conversation.go:
     <PlandexBlock lang="go">
     // modified part of loadConversation
     ...
     	messages = append(messages, openai.ChatCompletionMessage{
     		Role:       currentRole,
     		Content:    loadContent(filePath, currentRole),
     		Summarized: checkIfMsgSummarized(filePath, currentRole)
     	})
     ...
     </PlandexBlock>
   - Add the helper functions, checkIfMsgSummarized to check if a given message has already been summarized.

3. In the server/model/proposal/create.go, handle the token limit error by introducing a summarization step.

   - Before calling the CreateChatCompletionStream, check the number of tokens in the request if they exceed the limit.
   - If they do, find the pairs of conversation and attempt to summarize starting from the second.
   - Use the model.Summarize() function provided by the system in step 2 above to handle this summarization.

4. Lastly, handle the superfluous WriteHeader call.
   - For the superfluous WriteHeader call, ensure that all response modifications (like setting headers) are made before calling WriteHeader() and before writing to the response body.

Note: It's still necessary to track which parts of the conversation have already been summarized and avoid summarizing them again if the token limit is still exceeded. This can be achieved by storing a "summarized" flag in each chat message (ChatCompletionMessage).
