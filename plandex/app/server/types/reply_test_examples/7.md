**Updating `file_map/map.go`**: I'll add debug logging to the mapMarkup function between the function declaration and the end of the file.

- file_map/map.go:
<PlandexBlock lang="go">
// ... existing code ...

func mapMarkup(node *tree_sitter.Node, content []byte) []Definition {
    if verboseLogging {
        fmt.Println("mapMarkup called with node type:", node.Type())
    }
    
    var defs []Definition
    cursor := tree_sitter.NewTreeCursor(node)
    defer cursor.Close()

    if cursor.GoToFirstChild() {
        if verboseLogging {
            fmt.Println("cursor went to first child")
        }
        
        for {
            node := cursor.CurrentNode()
            if verboseLogging {
                fmt.Println("processing node type:", node.Type())
            }
            
            if node.Type() == "element" {
                if verboseLogging {
                    fmt.Println("found element node")
                }
                
                // Get tag name from start_tag
                if startTag := node.ChildByFieldName("start_tag"); startTag != nil {
                    if verboseLogging {
                        fmt.Println("found start_tag:", startTag.Type())
                    }
                    
                    if nameNode := startTag.ChildByFieldName("name"); nameNode != nil {
                        if verboseLogging {
                            fmt.Printf("found name node: %s with content: %s\n", 
                                nameNode.Type(), string(nameNode.Content(content)))
                        }
                        
                        def := Definition{
                            Type:      "tag",
                            Line:      int(node.StartPoint().Row) + 1,
                            Signature: string(nameNode.Content(content)),
                        }

                        // Get class and id attributes
                        var attrs []string
                        for i := 0; i < int(startTag.ChildCount()); i++ {
                            child := startTag.Child(i)
                            if verboseLogging {
                                fmt.Printf("checking child %d type: %s\n", i, child.Type())
                            }
                            
                            if child.Type() == "attribute" {
                                if verboseLogging {
                                    fmt.Println("found attribute node")
                                }
                                
                                if attrName := child.ChildByFieldName("name"); attrName != nil {
                                    name := string(attrName.Content(content))
                                    if verboseLogging {
                                        fmt.Printf("found attribute name: %s\n", name)
                                    }
                                    
                                    if name == "class" || name == "id" {
                                        if attrValue := child.ChildByFieldName("value"); attrValue != nil {
                                            if verboseLogging {
                                                fmt.Printf("found attribute value: %s\n", 
                                                    string(attrValue.Content(content)))
                                            }
                                            attrs = append(attrs, fmt.Sprintf("%s=%q", 
                                                name, attrValue.Content(content)))
                                        }
                                    }
                                }
                            }
                        }
                        def.TagAttrs = attrs

                        // Recursively process children
                        if verboseLogging {
                            fmt.Println("recursively processing children")
                        }
                        def.Children = mapMarkup(node, content)
                        defs = append(defs, def)
                    } else if verboseLogging {
                        fmt.Println("no name node found")
                    }
                } else if verboseLogging {
                    fmt.Println("no start_tag found")
                }
            }

            if !cursor.GoToNextSibling() {
                if verboseLogging {
                    fmt.Println("no more siblings")
                }
                break
            }
        }
    } else if verboseLogging {
        fmt.Println("cursor could not go to first child")
    }

    if verboseLogging {
        fmt.Printf("returning %d definitions\n", len(defs))
    }
    return defs
}

// ... existing code ...
</PlandexBlock>