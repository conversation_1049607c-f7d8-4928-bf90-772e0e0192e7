I'll move the game-related files to the game directory.

### Move Files

- src/game.c → src/game/game.c
- src/game.h → src/game/game.h

<EndPlandexFileOps/>

Now I'll remove the README.md file as requested.

### Remove Files

- src/README.md

<EndPlandexFileOps/>

Then I'll reset the changes to the Makefile.

### Reset Changes

- Makefile

<EndPlandexFileOps/>

Now I'll write a new Makefile.

- Makefile:

<PlandexBlock lang="bash">
# Create necessary directories if they don't exist
mkdir -p src include obj bin

# Check if Homebrew is installed
if ! command -v brew &> /dev/null; then
    echo "Homebrew is not installed. Please install Homebrew first."
    exit 1
fi
</PlandexBlock>