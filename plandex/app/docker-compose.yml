services:
  plandex-postgres:
    image: postgres:latest
    restart: always
    environment:
      POSTGRES_PASSWORD: plandex
      POSTGRES_USER: plandex
      POSTGRES_DB: plandex
    ports:
      - "5432:5432"
    volumes:
      - plandex-db:/var/lib/postgresql/data
    networks:
      - plandex-network
  plandex-server:
    image: plandexai/plandex-server:latest
    volumes:
      - plandex-files:/plandex-server
    ports:
      - "8099:8099"
    environment:
      DATABASE_URL: "************************************************/plandex?sslmode=disable"
      GOENV: development
      LOCAL_MODE: 1
      PLANDEX_BASE_DIR: /plandex-server
    networks:
      - plandex-network
    depends_on:
      - plandex-postgres
    command: [ "/bin/sh", "-c", "/scripts/wait-for-it.sh plandex-postgres:5432 -- ./plandex-server" ]

networks:
  plandex-network:
    driver: bridge

volumes:
  plandex-db:
  plandex-files: