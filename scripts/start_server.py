
#!/usr/bin/env python3
import os
import sys
import argparse
import logging
import socket
from dotenv import load_dotenv

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def is_port_in_use(port):
    """Check if a port is in use."""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def find_available_port(start_port, max_attempts=10):
    """Find an available port starting from start_port."""
    port = start_port
    for _ in range(max_attempts):
        if not is_port_in_use(port):
            return port
        port += 1
    raise RuntimeError(f"Could not find an available port after {max_attempts} attempts")

def main():
    """Start the Flask API server for knowledge pipeline ingestion."""
    
    # Load environment variables
    load_dotenv()
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Start the Flask API server for knowledge pipeline ingestion")
    parser.add_argument("--port", "-p", type=int, default=5002, help="Port to run the server on")
    parser.add_argument("--host", "-H", default="0.0.0.0", help="Host to run the server on")
    parser.add_argument("--reload", "-r", action="store_true", help="Enable auto-reload")
    
    args = parser.parse_args()
    
    # Find an available port
    try:
        port = find_available_port(args.port)
        if port != args.port:
            logger.info(f"Port {args.port} is in use, using port {port} instead")
    except RuntimeError as e:
        logger.error(str(e))
        sys.exit(1)
    
    # Check if required environment variables are set
    required_env_vars = ["SUPABASE_URL", "SUPABASE_KEY"]
    missing_env_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_env_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_env_vars)}")
        logger.error("Please set these variables in the .env file or environment")
        sys.exit(1)
    
    # Start the server
    logger.info(f"Starting Flask API server on {args.host}:{port}")
    
    try:
        # Import the Flask app
        from knowledge_pipeline.ingestion_api import app
        
        # Configure Flask app for development if reload is enabled
        if args.reload:
            app.config['DEBUG'] = True
        
        # Start the Flask development server
        app.run(
            host=args.host,
            port=port,
            debug=args.reload,
            use_reloader=args.reload
        )
    except Exception as e:
        logger.error(f"Error starting server: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
