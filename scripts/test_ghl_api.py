#!/usr/bin/env python3
"""
Test script to explore GHL API endpoints
"""

import os
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '.env'))

GHL_LOCATION_API_KEY = (
    os.getenv("GHL_LOCATION_KEY") or 
    os.getenv("GHL_LOCATION_API_KEY") or 
    os.getenv("GHL_API_KEY")
)
GHL_LOCATION_ID = os.getenv("GHL_LOCATION_ID")

def test_endpoint(url, headers, method="GET"):
    """Test a specific endpoint"""
    try:
        if method == "GET":
            response = requests.get(url, headers=headers, timeout=10)
        else:
            response = requests.post(url, headers=headers, timeout=10)
        
        print(f"✅ {method} {url}: {response.status_code}")
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"   Response: {json.dumps(data, indent=2)[:200]}...")
            except:
                print(f"   Response: {response.text[:200]}...")
        elif response.status_code in [401, 403]:
            print("   ❌ Authentication issue")
        elif response.status_code == 404:
            print("   ❌ Endpoint not found")
        else:
            print(f"   Response: {response.text[:200]}...")
        return response.status_code
    except Exception as e:
        print(f"❌ {method} {url}: Error - {e}")
        return None

def main():
    if not GHL_LOCATION_API_KEY:
        print("❌ No GHL API key found in environment variables")
        return
    
    print(f"🔑 Using API key: {GHL_LOCATION_API_KEY[:20]}...")
    print(f"📍 Location ID: {GHL_LOCATION_ID}")
    print()
    
    headers = {
        "Authorization": f"Bearer {GHL_LOCATION_API_KEY}",
        "Accept": "application/json",
        "Content-Type": "application/json"
    }
    
    # Test various API endpoints
    endpoints_to_test = [
        # Base API endpoints
        "https://services.leadconnectorhq.com/",
        "https://rest.gohighlevel.com/v1/",
        
        # Webhook endpoints
        "https://services.leadconnectorhq.com/hooks",
        "https://services.leadconnectorhq.com/hooks/",
        f"https://services.leadconnectorhq.com/locations/{GHL_LOCATION_ID}/hooks",
        f"https://services.leadconnectorhq.com/locations/{GHL_LOCATION_ID}/hooks/",
        
        # Alternative webhook endpoints
        "https://rest.gohighlevel.com/v1/hooks",
        "https://rest.gohighlevel.com/v1/hooks/",
        f"https://rest.gohighlevel.com/v1/locations/{GHL_LOCATION_ID}/hooks",
        f"https://rest.gohighlevel.com/v1/locations/{GHL_LOCATION_ID}/hooks/",
        
        # Contact endpoints (to verify API key works)
        "https://services.leadconnectorhq.com/contacts/",
        f"https://services.leadconnectorhq.com/locations/{GHL_LOCATION_ID}/contacts/",
        "https://rest.gohighlevel.com/v1/contacts/",
        f"https://rest.gohighlevel.com/v1/locations/{GHL_LOCATION_ID}/contacts/",
    ]
    
    print("🧪 Testing GHL API endpoints...")
    print("=" * 50)
    
    working_endpoints = []
    for endpoint in endpoints_to_test:
        if endpoint and GHL_LOCATION_ID in endpoint:  # Only test location-specific endpoints if we have location ID
            status = test_endpoint(endpoint, headers)
            if status == 200:
                working_endpoints.append(endpoint)
        elif endpoint and GHL_LOCATION_ID not in endpoint:  # Test non-location specific endpoints
            status = test_endpoint(endpoint, headers)
            if status == 200:
                working_endpoints.append(endpoint)
    
    print("\n📊 Summary:")
    print("=" * 20)
    if working_endpoints:
        print("✅ Working endpoints:")
        for endpoint in working_endpoints:
            print(f"   - {endpoint}")
    else:
        print("❌ No working endpoints found")
        print("\n🔧 Troubleshooting suggestions:")
        print("   1. Verify your GHL API key is correct")
        print("   2. Check if the API key has the right permissions")
        print("   3. Ensure the location ID is correct")
        print("   4. Try using the GHL dashboard to create webhooks manually")

if __name__ == "__main__":
    main()
