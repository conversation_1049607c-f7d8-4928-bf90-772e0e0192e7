#!/bin/bash
# Complete setup script for GHL to n8n integration

set -e

echo "🚀 GHL to n8n Integration Setup"
echo "==============================="

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENV_FILE="$PROJECT_ROOT/.env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if .env file exists
    if [ ! -f "$ENV_FILE" ]; then
        log_warning ".env file not found. Creating template..."
        create_env_template
    fi
    
    log_success "Prerequisites check completed"
}

# Create .env template if it doesn't exist
create_env_template() {
    cat > "$ENV_FILE" << 'EOF'
# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/aios
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_key

# n8n Configuration
N8N_API_KEY=your_n8n_api_key
WEBHOOK_URL=http://localhost:5678

# GHL Configuration
GHL_API_KEY=your_ghl_api_key
GHL_LOCATION_ID=your_ghl_location_id
GHL_LOCATION_API_KEY=your_ghl_location_api_key

# AI API Keys
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# Other Configuration
WEBHOOK_SECRET=your_webhook_secret
RETOOL_API_KEY=your_retool_api_key
RETOOL_ORG_ID=your_retool_org_id
EOF
    
    log_warning "Please update the .env file with your actual configuration values"
}

# Start Docker services
start_services() {
    log_info "Starting Docker services..."
    
    cd "$PROJECT_ROOT"
    
    # Stop any existing services
    docker-compose down 2>/dev/null || true
    
    # Start services
    docker-compose up -d
    
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Check if services are healthy
    if docker-compose ps | grep -q "unhealthy"; then
        log_error "Some services are unhealthy. Please check the logs:"
        docker-compose logs
        exit 1
    fi
    
    log_success "Docker services started successfully"
}

# Deploy workflows
deploy_workflows() {
    log_info "Deploying n8n workflows..."
    
    cd "$PROJECT_ROOT"
    
    # Set environment variables for the deployment script
    export N8N_URL="http://localhost:5678"
    
    # Run the workflow deployment script
    if [ -f "scripts/deploy_n8n_workflows.sh" ]; then
        ./scripts/deploy_n8n_workflows.sh
    else
        log_error "Workflow deployment script not found"
        exit 1
    fi
    
    log_success "Workflows deployed successfully"
}

# Test the integration
test_integration() {
    log_info "Testing GHL integration..."
    
    cd "$PROJECT_ROOT"
    
    # Run the test script
    if [ -f "scripts/test_ghl_workflow.py" ]; then
        python3 scripts/test_ghl_workflow.py
    else
        log_error "Test script not found"
        exit 1
    fi
    
    log_success "Integration test completed"
}

# Display final instructions
show_final_instructions() {
    echo ""
    log_success "GHL to n8n Integration Setup Complete!"
    echo "======================================"
    echo ""
    echo "🔗 Access URLs:"
    echo "   n8n Dashboard: http://localhost:5678"
    echo "   API Health: http://localhost:5002/api/v1/health"
    echo ""
    echo "📋 Webhook URLs for GHL:"
    echo "   Lead Webhook: http://localhost:5678/webhook/ghl/lead"
    echo "   Alert Webhook: http://localhost:5678/webhook/util/ghl-alert"
    echo ""
    echo "🔧 Next Steps:"
    echo "   1. Configure the webhook URLs in your GHL account"
    echo "   2. Update your .env file with actual API keys"
    echo "   3. Test with real GHL data"
    echo "   4. Monitor n8n execution logs"
    echo ""
    echo "📚 Useful Commands:"
    echo "   View logs: docker-compose logs -f"
    echo "   Restart services: docker-compose restart"
    echo "   Test workflow: python3 scripts/test_ghl_workflow.py"
    echo ""
}

# Main execution
main() {
    log_info "Starting GHL to n8n integration setup..."
    
    check_prerequisites
    start_services
    deploy_workflows
    test_integration
    show_final_instructions
    
    log_success "Setup completed successfully!"
}

# Handle script arguments
case "${1:-}" in
    "prerequisites")
        check_prerequisites
        ;;
    "services")
        start_services
        ;;
    "workflows")
        deploy_workflows
        ;;
    "test")
        test_integration
        ;;
    "")
        main
        ;;
    *)
        echo "Usage: $0 [prerequisites|services|workflows|test]"
        echo "  prerequisites - Check system requirements"
        echo "  services     - Start Docker services only"
        echo "  workflows    - Deploy workflows only"
        echo "  test         - Test integration only"
        echo "  (no args)    - Run complete setup"
        exit 1
        ;;
esac
