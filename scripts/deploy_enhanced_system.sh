#!/bin/bash
# Deploy Enhanced Lead Processing System

set -e

echo "🚀 Deploying Enhanced Lead Processing System"
echo "============================================"

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENV_FILE="$PROJECT_ROOT/.env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    # Check if .env file exists
    if [ ! -f "$ENV_FILE" ]; then
        log_error ".env file not found. Please create it first."
        exit 1
    fi
    
    # Check required environment variables
    source "$ENV_FILE"
    required_vars=("SUPABASE_URL" "SUPABASE_KEY" "SUPABASE_SERVICE_KEY")
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "Required environment variable $var is not set"
            exit 1
        fi
    done
    
    log_success "Prerequisites check completed"
}

# Build and start services
deploy_services() {
    log_info "Building and starting services..."
    
    cd "$PROJECT_ROOT"
    
    # Stop any existing services
    docker-compose down 2>/dev/null || true
    
    # Build and start services
    docker-compose up -d --build
    
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    services=("postgres" "n8n" "api")
    for service in "${services[@]}"; do
        if docker-compose ps "$service" | grep -q "Up"; then
            log_success "$service is running"
        else
            log_error "$service failed to start"
            docker-compose logs "$service"
            exit 1
        fi
    done
    
    log_success "All services started successfully"
}

# Deploy n8n workflows
deploy_workflows() {
    log_info "Deploying n8n workflows..."
    
    cd "$PROJECT_ROOT"
    
    # Wait for n8n to be fully ready
    max_attempts=30
    attempt=0
    while [ $attempt -lt $max_attempts ]; do
        if curl -f http://localhost:5678/healthz >/dev/null 2>&1; then
            break
        fi
        log_info "Waiting for n8n to be ready... (attempt $((attempt + 1))/$max_attempts)"
        sleep 10
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -eq $max_attempts ]; then
        log_error "n8n failed to become ready"
        exit 1
    fi
    
    # Deploy workflows
    if [ -f "scripts/deploy_n8n_workflows.sh" ]; then
        export N8N_URL="http://localhost:5678"
        ./scripts/deploy_n8n_workflows.sh
    else
        log_warning "Workflow deployment script not found"
    fi
    
    log_success "Workflows deployed successfully"
}

# Test the enhanced system
test_system() {
    log_info "Testing enhanced lead processing system..."
    
    cd "$PROJECT_ROOT"
    
    # Test API health
    if curl -f http://localhost:5002/api/v1/health >/dev/null 2>&1; then
        log_success "Enhanced API is healthy"
    else
        log_error "Enhanced API health check failed"
        return 1
    fi
    
    # Test n8n health
    if curl -f http://localhost:5678/healthz >/dev/null 2>&1; then
        log_success "n8n is healthy"
    else
        log_error "n8n health check failed"
        return 1
    fi
    
    # Test webhook endpoint
    webhook_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d '{"test": true, "contact": {"id": "test_123", "firstName": "Test", "email": "<EMAIL>"}}' \
        http://localhost:5678/webhook/ghl/lead)
    
    if echo "$webhook_response" | grep -q "success"; then
        log_success "Webhook endpoint is working"
    else
        log_warning "Webhook test returned: $webhook_response"
    fi
    
    # Run enhanced lead processing tests
    if [ -f "tests/test_enhanced_lead_processing.py" ]; then
        log_info "Running enhanced lead processing tests..."
        if python3 tests/test_enhanced_lead_processing.py; then
            log_success "Enhanced lead processing tests passed"
        else
            log_warning "Some enhanced lead processing tests failed"
        fi
    fi
    
    log_success "System testing completed"
}

# Setup GHL integration
setup_ghl_integration() {
    log_info "Setting up GHL integration..."
    
    # Check if GHL credentials are available
    if [ -n "$GHL_API_KEY" ] && [ -n "$GHL_LOCATION_ID" ]; then
        log_info "GHL credentials found, attempting webhook registration..."
        
        if [ -f "scripts/register_ghl_webhooks.py" ]; then
            # Try to register webhooks
            if python3 scripts/register_ghl_webhooks.py --all "http://localhost:5678"; then
                log_success "GHL webhooks registered successfully"
            else
                log_warning "GHL webhook registration failed - you may need to set them up manually"
            fi
        fi
    else
        log_warning "GHL credentials not found - skipping webhook registration"
        log_info "Please set GHL_API_KEY and GHL_LOCATION_ID in your .env file"
    fi
}

# Display final status and instructions
show_final_status() {
    echo ""
    log_success "Enhanced Lead Processing System Deployment Complete!"
    echo "=================================================="
    echo ""
    echo "🔗 Service URLs:"
    echo "   Enhanced API: http://localhost:5002/api/v1/health"
    echo "   n8n Dashboard: http://localhost:5678"
    echo "   PostgreSQL: localhost:5433"
    echo ""
    echo "📋 Webhook URLs for GHL:"
    echo "   Lead Webhook: http://localhost:5678/webhook/ghl/lead"
    echo "   Alert Webhook: http://localhost:5678/webhook/util/ghl-alert"
    echo ""
    echo "🔧 Enhanced Features Available:"
    echo "   ✅ Advanced Lead Scoring"
    echo "   ✅ Intelligent Qualification"
    echo "   ✅ Automated Follow-up Sequences"
    echo "   ✅ Workflow Orchestration"
    echo "   ✅ Real-time Processing"
    echo ""
    echo "📚 API Endpoints:"
    echo "   POST /api/v1/leads/process-enhanced"
    echo "   POST /api/v1/leads/score"
    echo "   POST /api/v1/leads/qualify"
    echo "   POST /api/v1/leads/followup/create"
    echo "   POST /api/v1/workflows/comping"
    echo "   POST /api/v1/workflows/followup"
    echo ""
    echo "🔧 Next Steps:"
    echo "   1. Configure GHL webhooks (if not done automatically)"
    echo "   2. Test with real lead data"
    echo "   3. Monitor execution logs"
    echo "   4. Customize scoring weights and qualification criteria"
    echo ""
    echo "📊 Monitoring Commands:"
    echo "   View logs: docker-compose logs -f"
    echo "   Check status: docker-compose ps"
    echo "   Test system: ./scripts/verify_ghl_integration.sh"
    echo ""
}

# Main execution
main() {
    log_info "Starting enhanced lead processing system deployment..."
    
    check_prerequisites
    deploy_services
    deploy_workflows
    test_system
    setup_ghl_integration
    show_final_status
    
    log_success "Deployment completed successfully!"
}

# Handle script arguments
case "${1:-}" in
    "prerequisites")
        check_prerequisites
        ;;
    "services")
        deploy_services
        ;;
    "workflows")
        deploy_workflows
        ;;
    "test")
        test_system
        ;;
    "ghl")
        setup_ghl_integration
        ;;
    "")
        main
        ;;
    *)
        echo "Usage: $0 [prerequisites|services|workflows|test|ghl]"
        echo "  prerequisites - Check system requirements"
        echo "  services     - Deploy Docker services only"
        echo "  workflows    - Deploy n8n workflows only"
        echo "  test         - Test system only"
        echo "  ghl          - Setup GHL integration only"
        echo "  (no args)    - Run complete deployment"
        exit 1
        ;;
esac
