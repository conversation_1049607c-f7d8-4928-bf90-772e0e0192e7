#!/bin/bash
# Verification script for GHL to n8n integration

set -e

echo "🔍 GHL to n8n Integration Verification"
echo "======================================"

# Configuration
N8N_URL="${N8N_URL:-http://localhost:5678}"
API_URL="${API_URL:-http://localhost:5002}"
WEBHOOK_URL="$N8N_URL/webhook/ghl/lead"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Helper functions
check_service() {
    local service_name="$1"
    local url="$2"
    local expected_status="${3:-200}"
    
    echo -n "🔍 Checking $service_name... "
    
    if response=$(curl -s -w "%{http_code}" -o /dev/null "$url" 2>/dev/null); then
        if [ "$response" = "$expected_status" ]; then
            echo -e "${GREEN}✅ OK${NC}"
            return 0
        else
            echo -e "${RED}❌ FAILED (HTTP $response)${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ UNREACHABLE${NC}"
        return 1
    fi
}

check_docker_service() {
    local service_name="$1"
    echo -n "🐳 Checking Docker service $service_name... "
    
    if docker-compose ps "$service_name" | grep -q "Up"; then
        echo -e "${GREEN}✅ RUNNING${NC}"
        return 0
    else
        echo -e "${RED}❌ NOT RUNNING${NC}"
        return 1
    fi
}

check_env_var() {
    local var_name="$1"
    local required="${2:-true}"
    
    echo -n "🔧 Checking $var_name... "
    
    if [ -n "${!var_name}" ]; then
        echo -e "${GREEN}✅ SET${NC}"
        return 0
    else
        if [ "$required" = "true" ]; then
            echo -e "${RED}❌ MISSING${NC}"
            return 1
        else
            echo -e "${YELLOW}⚠️  NOT SET${NC}"
            return 0
        fi
    fi
}

test_webhook() {
    echo "🧪 Testing webhook endpoint..."
    
    local test_payload='{
        "contact": {
            "id": "verification_test_123",
            "firstName": "Test",
            "lastName": "User",
            "email": "<EMAIL>",
            "phone": "+1234567890"
        },
        "eventType": "ContactCreate"
    }'
    
    echo "📤 Sending test payload to $WEBHOOK_URL"
    
    if response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$test_payload" \
        -w "%{http_code}" \
        "$WEBHOOK_URL" 2>/dev/null); then
        
        http_code="${response: -3}"
        response_body="${response%???}"
        
        if [ "$http_code" = "200" ]; then
            echo -e "${GREEN}✅ Webhook test successful${NC}"
            echo "📋 Response: $response_body"
            return 0
        else
            echo -e "${RED}❌ Webhook test failed (HTTP $http_code)${NC}"
            echo "📋 Response: $response_body"
            return 1
        fi
    else
        echo -e "${RED}❌ Webhook test failed (connection error)${NC}"
        return 1
    fi
}

main() {
    echo "🚀 Starting verification process..."
    echo ""
    
    local all_checks_passed=true
    
    # Check Docker services
    echo "📋 Docker Services Check"
    echo "------------------------"
    check_docker_service "postgres" || all_checks_passed=false
    check_docker_service "n8n" || all_checks_passed=false
    check_docker_service "api" || all_checks_passed=false
    echo ""
    
    # Check service endpoints
    echo "📋 Service Endpoints Check"
    echo "--------------------------"
    check_service "PostgreSQL" "http://localhost:5433" "000" || all_checks_passed=false
    check_service "n8n Health" "$N8N_URL/healthz" || all_checks_passed=false
    check_service "API Health" "$API_URL/api/v1/health" || all_checks_passed=false
    echo ""
    
    # Check environment variables
    echo "📋 Environment Variables Check"
    echo "------------------------------"
    check_env_var "SUPABASE_URL" || all_checks_passed=false
    check_env_var "SUPABASE_KEY" || all_checks_passed=false
    check_env_var "SUPABASE_SERVICE_KEY" || all_checks_passed=false
    check_env_var "N8N_API_KEY" false
    check_env_var "GHL_API_KEY" false
    check_env_var "GHL_LOCATION_ID" false
    echo ""
    
    # Test webhook functionality
    echo "📋 Webhook Functionality Test"
    echo "-----------------------------"
    test_webhook || all_checks_passed=false
    echo ""
    
    # Check workflow files
    echo "📋 Workflow Files Check"
    echo "-----------------------"
    echo -n "🔍 Checking main workflow file... "
    if [ -f "ghl-lead-workflow.json" ] && jq . ghl-lead-workflow.json >/dev/null 2>&1; then
        echo -e "${GREEN}✅ VALID${NC}"
    else
        echo -e "${RED}❌ INVALID OR MISSING${NC}"
        all_checks_passed=false
    fi
    
    echo -n "🔍 Checking additional workflows... "
    workflow_count=$(find n8n/workflows -name "*.json" -type f | wc -l)
    if [ "$workflow_count" -gt 0 ]; then
        echo -e "${GREEN}✅ FOUND ($workflow_count files)${NC}"
    else
        echo -e "${YELLOW}⚠️  NO ADDITIONAL WORKFLOWS${NC}"
    fi
    echo ""
    
    # Final summary
    echo "📊 Verification Summary"
    echo "======================"
    
    if [ "$all_checks_passed" = true ]; then
        echo -e "${GREEN}🎉 All checks passed! Your GHL to n8n integration is ready.${NC}"
        echo ""
        echo "🔗 Access URLs:"
        echo "   n8n Dashboard: $N8N_URL"
        echo "   API Health: $API_URL/api/v1/health"
        echo ""
        echo "📋 Webhook URL for GHL:"
        echo "   $WEBHOOK_URL"
        echo ""
        echo "🔧 Next steps:"
        echo "   1. Configure this webhook URL in your GHL account"
        echo "   2. Test with real GHL data"
        echo "   3. Monitor execution logs in n8n dashboard"
        return 0
    else
        echo -e "${RED}❌ Some checks failed. Please review the issues above.${NC}"
        echo ""
        echo "🔧 Troubleshooting:"
        echo "   1. Check the troubleshooting guide: docs/GHL_N8N_TROUBLESHOOTING.md"
        echo "   2. Review Docker logs: docker-compose logs -f"
        echo "   3. Verify your .env configuration"
        echo "   4. Run setup again: ./scripts/setup_ghl_integration.sh"
        return 1
    fi
}

# Run verification
main
exit_code=$?

echo ""
echo "🏁 Verification completed with exit code: $exit_code"
exit $exit_code
