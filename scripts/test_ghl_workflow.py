#!/usr/bin/env python3
"""
Test script for GHL to n8n workflow integration
"""

import os
import json
import requests
import time
from datetime import datetime

# Configuration
N8N_URL = os.getenv("N8N_URL", "http://localhost:5678")
WEBHOOK_URL = f"{N8N_URL}/webhook/ghl/lead"

def create_test_ghl_payload():
    """Create a realistic GHL webhook payload for testing"""
    return {
        "contact": {
            "id": "test_contact_123",
            "firstName": "<PERSON>",
            "lastName": "Doe",
            "email": "<EMAIL>",
            "phone": "+1234567890",
            "address1": "123 Main St",
            "address2": "Apt 4B",
            "city": "Anytown",
            "state": "CA",
            "postalCode": "90210",
            "country": "US",
            "customFields": [
                {
                    "name": "lead_type",
                    "value": "seller"
                },
                {
                    "name": "investment_experience",
                    "value": "intermediate"
                },
                {
                    "name": "annual_income",
                    "value": "75000"
                },
                {
                    "name": "preferred_contact_method",
                    "value": "phone"
                }
            ],
            "tags": ["hot_lead", "motivated_seller"],
            "notes": "Interested in selling property quickly"
        },
        "eventType": "ContactCreate",
        "timestamp": datetime.now().isoformat()
    }

def test_webhook_endpoint():
    """Test the webhook endpoint with a sample payload"""
    print("🧪 Testing GHL Webhook Endpoint")
    print("=" * 40)
    
    # Check if n8n is accessible
    try:
        health_response = requests.get(f"{N8N_URL}/healthz", timeout=10)
        if health_response.status_code == 200:
            print("✅ n8n is accessible")
        else:
            print(f"❌ n8n health check failed: {health_response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot reach n8n at {N8N_URL}: {e}")
        return False
    
    # Create test payload
    test_payload = create_test_ghl_payload()
    print(f"📤 Sending test payload to: {WEBHOOK_URL}")
    print(f"📋 Payload preview: {json.dumps(test_payload, indent=2)[:200]}...")
    
    try:
        # Send webhook request
        response = requests.post(
            WEBHOOK_URL,
            json=test_payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"📥 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print("✅ Webhook processed successfully!")
                print(f"📋 Response: {json.dumps(response_data, indent=2)}")
                return True
            except json.JSONDecodeError:
                print("✅ Webhook processed successfully (no JSON response)")
                print(f"📋 Response text: {response.text}")
                return True
        else:
            print(f"❌ Webhook failed with status {response.status_code}")
            print(f"📋 Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False

def test_multiple_scenarios():
    """Test multiple webhook scenarios"""
    print("\n🧪 Testing Multiple Scenarios")
    print("=" * 40)
    
    scenarios = [
        {
            "name": "Minimal Contact",
            "payload": {
                "contact": {
                    "id": "minimal_123",
                    "email": "<EMAIL>"
                }
            }
        },
        {
            "name": "Phone Only Contact",
            "payload": {
                "contact": {
                    "id": "phone_123",
                    "phone": "+1987654321",
                    "firstName": "Jane",
                    "lastName": "Smith"
                }
            }
        },
        {
            "name": "Full Contact Data",
            "payload": create_test_ghl_payload()
        }
    ]
    
    results = []
    for scenario in scenarios:
        print(f"\n📋 Testing: {scenario['name']}")
        try:
            response = requests.post(
                WEBHOOK_URL,
                json=scenario['payload'],
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            success = response.status_code == 200
            results.append({
                "scenario": scenario['name'],
                "success": success,
                "status_code": response.status_code
            })
            
            if success:
                print(f"✅ {scenario['name']}: SUCCESS")
            else:
                print(f"❌ {scenario['name']}: FAILED ({response.status_code})")
                
        except Exception as e:
            print(f"❌ {scenario['name']}: ERROR - {e}")
            results.append({
                "scenario": scenario['name'],
                "success": False,
                "error": str(e)
            })
    
    return results

def main():
    """Main test function"""
    print("🚀 GHL to n8n Workflow Test Suite")
    print("=" * 50)
    print(f"🔗 Testing webhook at: {WEBHOOK_URL}")
    print(f"⏰ Test started at: {datetime.now().isoformat()}")
    print()
    
    # Test basic webhook functionality
    basic_test_passed = test_webhook_endpoint()
    
    if basic_test_passed:
        # Test multiple scenarios
        scenario_results = test_multiple_scenarios()
        
        # Summary
        print("\n📊 Test Summary")
        print("=" * 40)
        successful_tests = sum(1 for result in scenario_results if result['success'])
        total_tests = len(scenario_results)
        
        print(f"✅ Successful tests: {successful_tests}/{total_tests}")
        
        if successful_tests == total_tests:
            print("🎉 All tests passed! GHL workflow is working correctly.")
            return True
        else:
            print("⚠️  Some tests failed. Check the logs above for details.")
            return False
    else:
        print("❌ Basic webhook test failed. Please check your n8n configuration.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
