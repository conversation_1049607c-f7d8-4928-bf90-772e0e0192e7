import os
import requests
import json
import argparse
import sys
from dotenv import load_dotenv
from urllib.parse import urlparse

# Load environment variables from .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '.env'))

# Try multiple environment variable names for GHL API key
GHL_LOCATION_API_KEY = (
    os.getenv("GHL_LOCATION_KEY") or
    os.getenv("GHL_LOCATION_API_KEY") or
    os.getenv("GHL_API_KEY")
)
GHL_LOCATION_ID = os.getenv("GHL_LOCATION_ID")
GHL_API_VERSION = "2021-07-28"

def validate_webhook_url(url: str) -> bool:
    """Validate that the webhook URL is properly formatted"""
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except:
        return False

def list_existing_webhooks():
    """List all existing webhooks in GHL"""
    if not GHL_LOCATION_API_KEY:
        print("Error: GHL API key not found in environment variables.")
        return []

    # Try multiple API endpoints as GHL API structure varies
    api_endpoints = [
        "https://services.leadconnectorhq.com/hooks/",
        f"https://services.leadconnectorhq.com/locations/{GHL_LOCATION_ID}/hooks/" if GHL_LOCATION_ID else None,
        "https://rest.gohighlevel.com/v1/hooks/",
        f"https://rest.gohighlevel.com/v1/locations/{GHL_LOCATION_ID}/hooks/" if GHL_LOCATION_ID else None
    ]

    # Filter out None values
    api_endpoints = [ep for ep in api_endpoints if ep is not None]

    headers = {
        "Authorization": f"Bearer {GHL_LOCATION_API_KEY}",
        "Version": GHL_API_VERSION,
        "Accept": "application/json"
    }

    for api_endpoint in api_endpoints:
        try:
            print(f"🔍 Trying endpoint: {api_endpoint}")
            response = requests.get(api_endpoint, headers=headers, timeout=10)
            response.raise_for_status()
            webhooks = response.json()

            print("✅ Successfully retrieved webhooks!")
            print("📋 Existing webhooks:")
            if isinstance(webhooks, list):
                if webhooks:
                    for webhook in webhooks:
                        print(f"  - {webhook.get('name', 'Unnamed')}: {webhook.get('url', 'No URL')} ({webhook.get('eventName', 'No event')})")
                else:
                    print("  No webhooks found.")
            else:
                print(f"  Response: {webhooks}")

            return webhooks if isinstance(webhooks, list) else []

        except requests.exceptions.HTTPError as e:
            print(f"❌ HTTP {e.response.status_code} error for {api_endpoint}")
            continue
        except Exception as e:
            print(f"❌ Error with {api_endpoint}: {e}")
            continue

    print("❌ Could not retrieve webhooks from any endpoint")
    return []

def register_ghl_webhook(webhook_url: str, event_type: str, name: str = "AIOS Lead Webhook"):
    """
    Registers a webhook in GoHighLevel for a specific event.

    Args:
        webhook_url (str): The URL the webhook should send data to.
        event_type (str): The GHL event type to subscribe to (e.g., 'ContactCreate', 'FormSubmit').
                          Refer to GHL API documentation for available event types.
        name (str): A descriptive name for the webhook.

    Returns:
        bool: True if registration was successful, False otherwise.
    """
    if not GHL_LOCATION_API_KEY:
        print("❌ Error: GHL API key not found in environment variables.")
        print("   Please ensure GHL_LOCATION_KEY, GHL_LOCATION_API_KEY, or GHL_API_KEY is set in your .env file.")
        return False

    if not validate_webhook_url(webhook_url):
        print(f"❌ Error: Invalid webhook URL: {webhook_url}")
        return False

    # Check for existing webhooks first
    existing_webhooks = list_existing_webhooks()
    for webhook in existing_webhooks:
        if webhook.get('url') == webhook_url and webhook.get('eventName') == event_type:
            print(f"⚠️  Webhook already exists for {event_type} at {webhook_url}")
            print(f"   Webhook ID: {webhook.get('id')}")
            return True

    # Try multiple API endpoints for webhook creation
    api_endpoints = [
        "https://services.leadconnectorhq.com/hooks/",
        f"https://services.leadconnectorhq.com/locations/{GHL_LOCATION_ID}/hooks/" if GHL_LOCATION_ID else None,
        "https://rest.gohighlevel.com/v1/hooks/",
        f"https://rest.gohighlevel.com/v1/locations/{GHL_LOCATION_ID}/hooks/" if GHL_LOCATION_ID else None
    ]

    # Filter out None values
    api_endpoints = [ep for ep in api_endpoints if ep is not None]

    headers = {
        "Authorization": f"Bearer {GHL_LOCATION_API_KEY}",
        "Version": GHL_API_VERSION,
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    payload = {
        "url": webhook_url,
        "eventName": event_type,
        "name": name
    }

    # Add location ID if available
    if GHL_LOCATION_ID:
        payload["locationId"] = GHL_LOCATION_ID

    print(f"🔄 Attempting to register webhook:")
    print(f"   Name: {name}")
    print(f"   URL: {webhook_url}")
    print(f"   Event: {event_type}")

    # Try each endpoint until one works
    for api_endpoint in api_endpoints:
        print(f"🔍 Trying endpoint: {api_endpoint}")

        try:
            response = requests.post(api_endpoint, headers=headers, json=payload, timeout=30)
            response.raise_for_status()

            response_data = response.json()
            print("✅ Webhook registration successful!")
            print(f"📋 Response: {json.dumps(response_data, indent=2)}")

            # GHL API for creating hooks might return a list or a single object.
            # Check for common success indicators.
            webhook_id = None
            if response_data and response_data.get("id"):
                webhook_id = response_data.get("id")
            elif isinstance(response_data, list) and response_data and response_data[0].get("id"):
                webhook_id = response_data[0].get("id")
            elif response_data.get("succeeded"):
                print("✅ Webhook registration reported as succeeded by GHL.")
                return True

            if webhook_id:
                print(f"🆔 Webhook ID: {webhook_id}")
                return True
            else:
                print("⚠️  Webhook registration response did not contain expected success indicators.")
                print("   Please verify in your GHL account.")
                return True  # Assume success if we got a 200 response

        except requests.exceptions.HTTPError as http_err:
            print(f"❌ HTTP {http_err.response.status_code} error for {api_endpoint}")
            if http_err.response:
                try:
                    error_data = http_err.response.json()
                    print(f"📋 Error details: {json.dumps(error_data, indent=2)}")
                except:
                    print(f"📋 Response content: {http_err.response.text}")
            continue
        except requests.exceptions.RequestException as req_err:
            print(f"❌ Request error for {api_endpoint}: {req_err}")
            continue
        except json.JSONDecodeError:
            print(f"❌ Error decoding JSON response from {api_endpoint}")
            continue
        except Exception as e:
            print(f"❌ Unexpected error with {api_endpoint}: {e}")
            continue

    print("❌ Could not register webhook with any endpoint")
    return False

def register_all_webhooks(base_url: str):
    """Register all required webhooks for AIOS"""
    webhooks_to_register = [
        {
            "url": f"{base_url}/webhook/ghl/lead",
            "event": "ContactCreate",
            "name": "AIOS Lead Creation Webhook"
        },
        {
            "url": f"{base_url}/webhook/ghl/lead",
            "event": "ContactUpdate",
            "name": "AIOS Lead Update Webhook"
        },
        {
            "url": f"{base_url}/webhook/ghl/lead",
            "event": "FormSubmit",
            "name": "AIOS Form Submission Webhook"
        }
    ]

    print(f"🚀 Registering all AIOS webhooks for base URL: {base_url}")
    print("=" * 60)

    results = []
    for webhook_config in webhooks_to_register:
        print(f"\n📝 Registering: {webhook_config['name']}")
        success = register_ghl_webhook(
            webhook_config["url"],
            webhook_config["event"],
            webhook_config["name"]
        )
        results.append({
            "name": webhook_config["name"],
            "success": success,
            "url": webhook_config["url"],
            "event": webhook_config["event"]
        })

    print(f"\n📊 Registration Summary:")
    print("=" * 30)
    successful = sum(1 for r in results if r["success"])
    total = len(results)

    for result in results:
        status = "✅" if result["success"] else "❌"
        print(f"{status} {result['name']} ({result['event']})")

    print(f"\n🎯 Success Rate: {successful}/{total}")
    return successful == total

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Register GoHighLevel Webhooks for AIOS",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Register a single webhook
  python scripts/register_ghl_webhooks.py "https://your-domain.com/webhook/ghl/lead" "ContactCreate" --name "AIOS New Contact Hook"

  # Register all AIOS webhooks at once
  python scripts/register_ghl_webhooks.py --all "https://your-domain.com"

  # List existing webhooks
  python scripts/register_ghl_webhooks.py --list
        """
    )

    # Mutually exclusive group for different modes
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("webhook_url", nargs="?", type=str, help="The URL for the webhook endpoint")
    group.add_argument("--all", type=str, metavar="BASE_URL", help="Register all AIOS webhooks for the given base URL")
    group.add_argument("--list", action="store_true", help="List all existing webhooks")

    parser.add_argument("event_type", nargs="?", type=str, help="The GHL event type to subscribe to (e.g., 'ContactCreate', 'FormSubmit')")
    parser.add_argument("--name", type=str, default="AIOS Webhook", help="A descriptive name for the webhook")

    args = parser.parse_args()

    # Validate arguments
    if args.list:
        print("📋 Listing existing GHL webhooks...")
        list_existing_webhooks()
        sys.exit(0)
    elif args.all:
        print(f"🚀 Registering all AIOS webhooks for: {args.all}")
        success = register_all_webhooks(args.all)
        sys.exit(0 if success else 1)
    elif args.webhook_url and args.event_type:
        print(f"📝 Registering single webhook...")
        success = register_ghl_webhook(args.webhook_url, args.event_type, args.name)
        if success:
            print("\n✅ Script finished successfully.")
        else:
            print("\n❌ Script finished with errors.")
        sys.exit(0 if success else 1)
    else:
        parser.error("When not using --all or --list, both webhook_url and event_type are required")
