#!/bin/bash
# Deploy n8n workflows to production instance

set -e

echo "🔄 Deploying n8n Workflows"
echo "=========================="

# Configuration
N8N_URL="${N8N_URL:-http://localhost:5678}"
N8N_API_KEY="${N8N_API_KEY}"
WORKFLOWS_DIR="./n8n/workflows"

# Check if n8n is running
echo "🔍 Checking n8n availability..."
if ! curl -f "$N8N_URL/healthz" > /dev/null 2>&1; then
    echo "❌ Error: n8n is not accessible at $N8N_URL"
    echo "Please ensure n8n is running and accessible."
    exit 1
fi

echo "✅ n8n is accessible"

# Function to import workflow
import_workflow() {
    local workflow_file="$1"
    local workflow_name=$(basename "$workflow_file" .json)
    
    echo "📥 Importing workflow: $workflow_name"
    
    # Check if workflow already exists
    existing_workflow=$(curl -s -H "X-N8N-API-KEY: $N8N_API_KEY" \
        "$N8N_URL/api/v1/workflows" | \
        jq -r ".data[] | select(.name == \"$(jq -r '.name' "$workflow_file")\") | .id" 2>/dev/null || echo "")
    
    if [ -n "$existing_workflow" ]; then
        echo "🔄 Updating existing workflow (ID: $existing_workflow)"
        curl -s -X PUT \
            -H "Content-Type: application/json" \
            -H "X-N8N-API-KEY: $N8N_API_KEY" \
            -d @"$workflow_file" \
            "$N8N_URL/api/v1/workflows/$existing_workflow" > /dev/null
    else
        echo "➕ Creating new workflow"
        curl -s -X POST \
            -H "Content-Type: application/json" \
            -H "X-N8N-API-KEY: $N8N_API_KEY" \
            -d @"$workflow_file" \
            "$N8N_URL/api/v1/workflows" > /dev/null
    fi
    
    echo "✅ Workflow $workflow_name deployed successfully"
}

# Check for API key
if [ -z "$N8N_API_KEY" ]; then
    echo "⚠️  Warning: N8N_API_KEY not set. Workflows will be imported but may not be accessible via API."
fi

# Import main GHL workflow
echo "📋 Deploying main GHL workflow..."
if [ -f "ghl-lead-workflow.json" ]; then
    import_workflow "ghl-lead-workflow.json"
else
    echo "❌ Error: ghl-lead-workflow.json not found"
    exit 1
fi

# Import additional workflows from n8n/workflows directory
echo "📋 Deploying additional workflows..."
for workflow_file in "$WORKFLOWS_DIR"/*.json; do
    if [ -f "$workflow_file" ]; then
        import_workflow "$workflow_file"
    fi
done

# Activate workflows
echo "🔄 Activating workflows..."
if [ -n "$N8N_API_KEY" ]; then
    # Get all workflows and activate them
    curl -s -H "X-N8N-API-KEY: $N8N_API_KEY" \
        "$N8N_URL/api/v1/workflows" | \
        jq -r '.data[] | select(.active == false) | .id' | \
        while read -r workflow_id; do
            if [ -n "$workflow_id" ]; then
                echo "🔄 Activating workflow ID: $workflow_id"
                curl -s -X POST \
                    -H "X-N8N-API-KEY: $N8N_API_KEY" \
                    "$N8N_URL/api/v1/workflows/$workflow_id/activate" > /dev/null
            fi
        done
fi

echo "✅ All workflows deployed and activated successfully!"

# Display webhook URLs
echo ""
echo "📋 Webhook URLs for GHL configuration:"
echo "======================================"
echo "Main Lead Webhook: $N8N_URL/webhook/ghl/lead"
echo "Alert Webhook: $N8N_URL/webhook/util/ghl-alert"
echo "Update GHL Webhook: $N8N_URL/webhook/aios/update-ghl"
echo ""
echo "🔧 Next steps:"
echo "1. Configure these webhook URLs in your GHL account"
echo "2. Test the workflow by sending a test lead"
echo "3. Monitor n8n execution logs for any issues"
