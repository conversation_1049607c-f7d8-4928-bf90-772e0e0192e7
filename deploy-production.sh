#!/bin/bash
# Production deployment script for AI-OS

set -e

echo "🚀 AI-OS Production Deployment Script"
echo "======================================"

# Check for required environment variables
if [ -z "$DOMAIN" ]; then
    echo "❌ Error: DOMAIN environment variable not set"
    echo "Usage: DOMAIN=your-domain.com ./deploy-production.sh"
    exit 1
fi

echo "📍 Deploying to domain: $DOMAIN"

# 1. Update environment for production
echo "🔧 Updating environment configuration..."
sed -i.bak "s|http://localhost:5678|https://$DOMAIN|g" .env
sed -i.bak "s|localhost|$DOMAIN|g" .env

# 2. Set up reverse proxy with Caddy
echo "🌐 Setting up reverse proxy..."
cat > Caddyfile << EOF
$DOMAIN {
    reverse_proxy /webhook/* localhost:5678
    reverse_proxy /api/* localhost:5002
    reverse_proxy /* localhost:5678

    encode gzip
    log {
        output file /var/log/caddy/aios.log
    }
}
EOF

# 3. Install Caddy if not present
if ! command -v caddy &> /dev/null; then
    echo "📦 Installing Caddy..."
    curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | sudo gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
    curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | sudo tee /etc/apt/sources.list.d/caddy-stable.list
    sudo apt update
    sudo apt install caddy -y
fi

# 4. Start services
echo "🐳 Starting Docker services..."
docker compose up -d

# 5. Start Caddy
echo "🔒 Starting Caddy with SSL..."
sudo caddy start --config Caddyfile

# 6. Wait for services to be healthy
echo "⏳ Waiting for services to be healthy..."
sleep 30

# 7. Deploy n8n workflows
echo "🔄 Deploying n8n workflows..."
export N8N_URL="https://$DOMAIN"
./scripts/deploy_n8n_workflows.sh

# 8. Test endpoints
echo "🧪 Testing endpoints..."
curl -f https://$DOMAIN/api/v1/health || echo "❌ API health check failed"
curl -f https://$DOMAIN/healthz || echo "❌ n8n health check failed"

# 9. Test webhook endpoint
echo "🧪 Testing webhook endpoint..."
curl -f https://$DOMAIN/webhook/ghl/lead -X POST -H "Content-Type: application/json" -d '{"test": true}' || echo "❌ Webhook test failed"

echo "✅ Production deployment complete!"
echo "📋 Next steps:"
echo "   1. Update GHL webhooks to: https://$DOMAIN/webhook/ghl/lead"
echo "   2. Test lead processing workflow"
echo "   3. Monitor logs: sudo caddy logs"
echo "   4. Check n8n workflow status at: https://$DOMAIN"